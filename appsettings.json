{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=*************;Initial Catalog=QL_nhatro;User ID=Ql_tro1234;Password=*********;Connect Timeout=30;Encrypt=True;Trust Server Certificate=True;Application Intent=ReadWrite;Multi Subnet Failover=False"}, "SmtpSettings": {"Host": "smtp.gmail.com", "Port": 587, "EnableSsl": true, "UserName": "<EMAIL>", "Password": "kpio wvgs loqi iujt"}}