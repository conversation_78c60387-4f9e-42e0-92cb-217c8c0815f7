:root {
    --primary: #4a90a4;
    --primary-light: #5aa3b8;
    --primary-dark: #2c5f6f;
    --secondary: #64748b;
    --accent: #f59e0b;
    --success: #10b981;
    --danger: #ef4444;
    --warning: #f59e0b;
    --info: #06b6d4;
    --light: #f8fafc;
    --white: #ffffff;
    --dark: #1e293b;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Banner Management Container */
.banner-management {
    margin-right: 0;
    padding: 30px;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--gray-50) 0%, #e2e8f0 100%);
    position: relative;
}

    .banner-management::before {
        content: '';
        position: fixed;
        top: 0;
        left: 280px;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(74, 144, 164, 0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        pointer-events: none;
        z-index: -1;
    }

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius-lg);
    padding: 40px;
    margin-bottom: 30px;
    color: var(--white);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="header-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23header-pattern)"/></svg>');
        pointer-events: none;
    }

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-left h1 {
    margin: 0 0 12px 0;
    font-size: 32px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

    .header-left h1 i {
        background: rgba(255, 255, 255, 0.2);
        padding: 10px;
        border-radius: 10px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

.header-left p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
    font-weight: 400;
}

/* Filters Section */
.filters-section {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(74, 144, 164, 0.1);
    backdrop-filter: blur(10px);
}

.filters-container {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 320px;
}

    .search-box i {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--primary);
        font-size: 16px;
        z-index: 2;
    }

    .search-box input {
        width: 100%;
        padding: 14px 14px 14px 50px;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        font-size: 15px;
        background: var(--white);
        transition: var(--transition);
        box-shadow: var(--shadow-sm);
    }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(74, 144, 164, 0.1);
            background: var(--white);
        }

.form-select {
    padding: 14px 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    background: var(--white);
    font-size: 15px;
    min-width: 180px;
    transition: var(--transition);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

    .form-select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 4px rgba(74, 144, 164, 0.1);
    }

/* Table Section */
.table-section {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(74, 144, 164, 0.1);
}

.table-container {
    overflow: hidden;
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
}

    .data-table th {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        padding: 20px 16px;
        text-align: left;
        font-weight: 600;
        color: var(--white);
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
    }

        .data-table th:first-child {
            border-top-left-radius: var(--border-radius);
        }

        .data-table th:last-child {
            border-top-right-radius: var(--border-radius);
        }

    .data-table td {
        padding: 18px 16px;
        border-bottom: 1px solid var(--gray-100);
        vertical-align: middle;
        transition: var(--transition);
    }

    .data-table tr:hover {
        background: linear-gradient(135deg, rgba(74, 144, 164, 0.02) 0%, rgba(90, 163, 184, 0.05) 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(74, 144, 164, 0.1);
    }

    .data-table tr:last-child td {
        border-bottom: none;
    }

/* Banner Image in Table */
.banner-image {
    width: 85px;
    height: 55px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

    .banner-image:hover {
        transform: scale(1.05);
        box-shadow: var(--shadow-md);
    }

.no-image {
    width: 85px;
    height: 55px;
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    border: 2px dashed var(--gray-300);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    font-size: 12px;
    font-weight: 500;
}

/* Status Badges */
.status-badge {
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-sm);
}

.status-active {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
    color: var(--white);
}

.status-inactive {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
    color: var(--white);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 10px;
}

.btn-action {
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

    .btn-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-action:hover::before {
        left: 100%;
    }

.btn-edit {
    background: linear-gradient(135deg, var(--warning) 0%, #f59e0b 100%);
    color: var(--white);
}

    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
    }

.btn-delete {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
    color: var(--white);
}

    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
    }

/* Mobile Cards */
.mobile-cards {
    display: none;
    padding: 25px;
    gap: 20px;
}

.mobile-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(74, 144, 164, 0.1);
    transition: var(--transition);
}

    .mobile-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.mobile-card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.mobile-card-id {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.mobile-card-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    box-shadow: var(--shadow-md);
}

.mobile-card-field {
    margin-bottom: 12px;
}

    .mobile-card-field label {
        font-weight: 600;
        color: var(--gray-700);
        display: block;
        margin-bottom: 6px;
        font-size: 14px;
    }

.mobile-card-actions {
    display: flex;
    gap: 12px;
    margin-top: 25px;
}

    .mobile-card-actions .btn {
        flex: 1;
        text-align: center;
        justify-content: center;
    }

/* Pagination */
.pagination-container {
    padding: 25px;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    border-top: 1px solid var(--gray-200);
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 25px;
    color: var(--gray-600);
    font-size: 14px;
    font-weight: 500;
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-numbers {
    display: flex;
    gap: 6px;
}

.page-number {
    padding: 10px 14px;
    border: 2px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-700);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

    .page-number:hover {
        background: var(--primary-light);
        color: var(--white);
        border-color: var(--primary-light);
        transform: translateY(-1px);
    }

    .page-number.active {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: var(--white);
        border-color: var(--primary);
        box-shadow: 0 4px 12px rgba(74, 144, 164, 0.3);
    }

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 99999;
    padding: 20px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

    .modal.show {
        display: flex !important;
    }

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 99998;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    position: relative;
    width: 100%;
    max-width: 550px;
    max-height: 85vh;
    margin: auto;
    margin-left: 450px;
    margin-bottom: 100px;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(74, 144, 164, 0.1);
    z-index: 99999;
    overflow: hidden;
}

.modal-header {
    padding: 25px 30px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
    border-bottom: 1px solid var(--gray-200);
}

    .modal-header h2 {
        margin: 0;
        font-size: 24px;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 700;
    }

        .modal-header h2 i {
            color: var(--primary);
            background: rgba(74, 144, 164, 0.1);
            padding: 8px;
            border-radius: 8px;
        }

.modal-close {
    background: none;
    border: none;
    font-size: 22px;
    cursor: pointer;
    color: var(--gray-400);
    padding: 10px;
    border-radius: 8px;
    transition: var(--transition);
}

    .modal-close:hover {
        background: var(--gray-100);
        color: var(--gray-600);
        transform: scale(1.1);
    }

.modal-body {
    padding: 30px;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
    -webkit-overflow-scrolling: touch;
}

.modal-footer {
    padding: 25px 30px;
    background: var(--gray-50);
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    border-top: 1px solid var(--gray-200);
    flex-shrink: 0;
    margin-top: auto;
}

/* Form Styles */
.form-group {
    margin-bottom: 22px;
}

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: var(--gray-700);
        font-size: 14px;
    }

.required {
    color: var(--danger);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background: var(--white);
    box-shadow: var(--shadow-sm);
}

    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 4px rgba(74, 144, 164, 0.1);
    }

/* iOS Safari fixes */
.form-group input,
.form-group textarea,
.form-group select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-group select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.char-counter {
    text-align: right;
    margin-top: 6px;
    font-size: 12px;
    color: var(--gray-500);
    font-weight: 500;
}

.error-message {
    color: var(--danger);
    font-size: 13px;
    margin-top: 6px;
    display: none;
    font-weight: 500;
}

    .error-message.show {
        display: block;
    }

/* File Upload */
.file-upload {
    position: relative;
    border: 2px dashed var(--primary-light);
    border-radius: var(--border-radius);
    padding: 50px 25px;
    text-align: center;
    background: linear-gradient(135deg, rgba(74, 144, 164, 0.02) 0%, rgba(90, 163, 184, 0.05) 100%);
    transition: var(--transition);
    cursor: pointer;
}

    .file-upload:hover {
        border-color: var(--primary);
        background: linear-gradient(135deg, rgba(74, 144, 164, 0.05) 0%, rgba(90, 163, 184, 0.1) 100%);
        transform: translateY(-2px);
    }

    .file-upload input[type="file"] {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

.file-upload-display i {
    font-size: 40px;
    color: var(--primary);
    margin-bottom: 15px;
    display: block;
}

.file-upload-display span {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
    font-size: 16px;
}

.file-upload-display small {
    color: var(--gray-500);
    font-size: 14px;
}

/* Image Preview */
.image-preview {
    position: relative;
    margin-top: 20px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

    .image-preview img {
        width: 100%;
        height: 220px;
        object-fit: cover;
        display: block;
    }

.image-preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    backdrop-filter: blur(4px);
}

.image-preview:hover .image-preview-overlay {
    opacity: 1;
}

.remove-image-btn {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

    .remove-image-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
    }

/* Buttons */
.btn {
    padding: 14px 28px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--white);
}

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(74, 144, 164, 0.4);
    }

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

    .btn-secondary:hover {
        background: var(--gray-300);
        transform: translateY(-1px);
    }

.btn-danger {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
    color: var(--white);
}

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    }

.btn-outline {
    background: transparent;
    color: var(--gray-600);
    border: 2px solid var(--gray-300);
}

    .btn-outline:hover:not(:disabled) {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
        transform: translateY(-1px);
    }

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 100000;
    backdrop-filter: blur(4px);
}

.loading-content {
    text-align: center;
    color: var(--gray-700);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Alert Styles */
.alert {
    padding: 18px 24px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: var(--shadow-md);
    border-left: 4px solid;
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    border-left-color: var(--success);
}

.alert-error {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border-left-color: var(--danger);
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-left-color: var(--warning);
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border-left-color: var(--info);
}

.alert-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 18px;
    margin-left: auto;
    padding: 6px;
    border-radius: 6px;
    opacity: 0.7;
    transition: var(--transition);
}

    .alert-close:hover {
        opacity: 1;
        background: rgba(0, 0, 0, 0.1);
        transform: scale(1.1);
    }

/* Responsive Design */
@media (max-width: 1024px) {
    .banner-management {
        margin-left: 0;
        margin-right: 0;
        padding: 20px;
    }

        .banner-management::before {
            left: 0;
        }
}

@media (max-width: 768px) {
    .banner-management {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 25px;
        text-align: center;
    }

    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .table-responsive {
        display: none;
    }

    .mobile-cards {
        display: flex;
        flex-direction: column;
    }

    .pagination-container {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    .modal.show {
        padding: 10px;
        align-items: flex-start;
        display: flex !important;
    }

        .modal.show .modal-content {
            max-width: calc(100vw - 20px);
            margin: auto;
            max-height: calc(100vh - 40px);
            width: 100%;
            display: flex;
            flex-direction: column;
        }

        .modal.show .modal-header {
            padding: 15px 20px;
            flex-shrink: 0;
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
        }

            .modal.show .modal-header h2 {
                font-size: 18px;
                gap: 8px;
            }

        .modal.show .modal-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .modal.show .modal-footer {
            padding: 15px 20px;
            flex-direction: column;
            gap: 10px;
            flex-shrink: 0;
            background: var(--white);
            border-top: 1px solid var(--gray-200);
        }

            .modal.show .modal-footer .btn {
                width: 100%;
                justify-content: center;
                padding: 12px 20px;
                font-size: 14px;
                min-height: 44px;
            }

    .form-group {
        margin-bottom: 18px;
    }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 10px 14px;
            font-size: 16px; /* Prevents zoom on iOS */
            min-height: 44px;
        }

    .file-upload {
        padding: 30px 20px;
    }

    .file-upload-display i {
        font-size: 32px;
    }

    .delete-modal {
        max-width: calc(100vw - 20px) !important;
    }
}

@media (max-width: 480px) {
    .banner-management {
        padding: 10px;
    }

    .page-header {
        padding: 25px 20px;
    }

    .header-left h1 {
        font-size: 26px;
    }

    .page-numbers {
        flex-wrap: wrap;
        justify-content: center;
    }

    .btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .modal.show {
        padding: 0;
        align-items: flex-start;
        display: flex !important;
    }

        .modal.show .modal-content {
            max-width: calc(100vw - 10px);
            margin: 5px auto;
            max-height: calc(100vh - 10px);
            border-radius: 8px;
            width: 100%;
            display: flex;
            flex-direction: column;
        }

        .modal.show .modal-header {
            padding: 12px 15px;
            border-radius: 0;
            flex-shrink: 0;
        }

            .modal.show .modal-header h2 {
                font-size: 16px;
            }

        .modal.show .modal-body {
            padding: 15px;
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .modal.show .modal-footer {
            padding: 12px 15px;
            gap: 8px;
            flex-shrink: 0;
            position: relative;
            bottom: auto;
            background: var(--white);
            border-top: 1px solid var(--gray-200);
        }

            .modal.show .modal-footer .btn {
                min-height: 44px;
                flex: 1;
            }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 12px 14px;
        min-height: 44px;
    }

    .file-upload {
        padding: 25px 15px;
    }

    .delete-modal {
        max-width: 100vw !important;
    }

        .delete-modal .modal-content {
            max-height: 50vh;
            margin: 25vh auto 0;
        }

    .modal-close {
        padding: 6px;
        font-size: 18px;
        min-width: 44px;
        min-height: 44px;
    }
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

/* Hover effects */
.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(74, 144, 164, 0.3);
}

/* Delete Modal Specific Styles */
.delete-modal {
    max-width: 450px !important;
}

    .delete-modal .modal-body {
        text-align: center;
    }

        .delete-modal .modal-body p {
            margin-bottom: 15px;
            font-size: 16px;
            line-height: 1.5;
        }

    .delete-modal .modal-footer {
        justify-content: center;
        gap: 20px;
    }

/* Ensure modal is always centered and visible */
.modal.show {
    align-items: center !important;
    justify-content: center !important;
}

    .modal.show .modal-content {
        margin: auto !important;
    }

/* Ensure footer buttons are always visible */
.modal-footer .btn {
    white-space: nowrap;
    min-width: 100px;
}
