﻿#live-notification {
    position: fixed;
    top: 75px;
    right: 20px;
    min-width: 250px;
    padding: 16px 20px;
    border-radius: 10px;
    font-family: 'Segoe UI', Roboto, -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 1.4;
    color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.4s ease, transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateY(-20px) scale(0.95);
    display: flex;
    align-items: center;
    overflow: hidden;
    backdrop-filter: blur(5px);
}

    #live-notification.hidden {
        display: none;
    }

    #live-notification.show {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    /* Status icon container */
    #live-notification::before {
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-right: 12px;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
    }

    /* Color themes with improved contrast and gradients */
    #live-notification.success {
        background: linear-gradient(135deg, #28a745, #20c997);
        border-left: 4px solid #1e7e34;
    }

        #live-notification.success::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
        }

    #live-notification.error {
        background: linear-gradient(135deg, #dc3545, #e83e8c);
        border-left: 4px solid #bd2130;
    }

        #live-notification.error::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
        }

    #live-notification.info {
        background: linear-gradient(135deg, #17a2b8, #0dcaf0);
        border-left: 4px solid #138496;
    }

        #live-notification.info::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='16' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12.01' y2='8'%3E%3C/line%3E%3C/svg%3E");
        }

    #live-notification.warning {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        border-left: 4px solid #d39e00;
        color: #212529;
    }

        #live-notification.warning::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23212529' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
        }

    /* Close button */
    #live-notification .close-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        opacity: 0.7;
        cursor: pointer;
        transition: opacity 0.2s;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
        background-size: contain;
    }

        #live-notification .close-btn:hover {
            opacity: 1;
        }

    #live-notification.warning .close-btn {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23212529' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
    }

    /* Progress bar for auto-dismiss */
    #live-notification .progress-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.3);
    }

        #live-notification .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            transform: translateX(-100%);
            animation: countdown 5s linear forwards;
        }

@keyframes countdown {
    to {
        transform: translateX(0);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #live-notification {
        min-width: unset;
        max-width: calc(100% - 40px);
        left: 20px;
        right: 20px;
        text-align: center;
    }
}

/* Animation for removal */
#live-notification.remove {
    opacity: 0;
    transform: translateX(100%);
    transition: opacity 0.3s ease, transform 0.3s ease;
}
