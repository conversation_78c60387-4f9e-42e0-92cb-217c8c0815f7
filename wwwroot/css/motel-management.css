/* Motel Management Styles */
.motel-management {
    padding: 20px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    color: white;
    box-shadow: 0 8px 25px rgba(74, 144, 164, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.header-left h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-left p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

/* Filters Section */
.filters-section {
    background: white;
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.filters-container {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 16px;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #4a90a4;
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}

.filter-group {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-group select {
    padding: 10px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 14px;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #4a90a4;
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}

/* Table Section */
.table-section {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.table-container {
    overflow-x: auto;
}

.motels-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.motels-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 18px 15px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
}

.motels-table td {
    padding: 15px;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.motels-table tr:hover {
    background: #f8fafc;
}

.motels-table .actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.motels-table .actions .btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

/* Mobile Cards */
.mobile-cards {
    display: none;
    padding: 20px;
    gap: 15px;
    flex-direction: column;
}

.motel-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border-left: 4px solid #4a90a4;
}

.motel-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.motel-card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.motel-card-id {
    background: #4a90a4;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.motel-card-info {
    margin-bottom: 15px;
}

.motel-card-info .info-row {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
}

.motel-card-info .info-label {
    font-weight: 500;
    color: #6b7280;
    min-width: 80px;
    font-size: 13px;
}

.motel-card-info .info-value {
    color: #374151;
    font-size: 13px;
}

.motel-card-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #f1f5f9;
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

/* Buttons */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(74, 144, 164, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 144, 164, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

.btn-outline {
    background: white;
    color: #4a90a4;
    border: 2px solid #4a90a4;
}

.btn-outline:hover:not(:disabled) {
    background: #4a90a4;
    color: white;
}

.btn-outline:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-edit {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 6px 12px;
    font-size: 12px;
}

.btn-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-delete {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 6px 12px;
    font-size: 12px;
}

.btn-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-top: 1px solid #e5e7eb;
}

.pagination-info {
    color: #6b7280;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    background: white;
    color: #4a90a4;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.page-number:hover:not(.active) {
    background: #f8fafc;
    border-color: #4a90a4;
}

.page-number.active {
    background: #4a90a4;
    color: white;
    border-color: #4a90a4;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 99999;
    align-items: flex-start;
    padding-top: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 10px;
    box-sizing: border-box;
}

.modal.show {
    display: flex;
    opacity: 1;
}

.modal-content {
    background: white;
    border-radius: 16px;
    width: 100%;
    max-width: none;
    max-height: calc(100vh - 40px);
    overflow: hidden;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
    position: relative;
    margin: 0;
    display: flex;
    flex-direction: column;
}

.modal.show .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 20px;
    color: #6b7280;
    padding: 5px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 180px);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 20px;
    border-top: 1px solid #e5e7eb;
    background: #f8fafc;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.required {
    color: #ef4444;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4a90a4;
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #ef4444;
}

/* Delete Modal */
.delete-modal .modal-body {
    text-align: center;
    padding: 30px;
}

.delete-modal .modal-body p {
    margin-bottom: 15px;
    font-size: 16px;
}

.text-danger {
    color: #ef4444;
}

/* Loading */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid #4a90a4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alert Messages */
.alert {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    animation: slideInDown 0.3s ease;
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #fecaca;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .filters-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-group {
        width: 100%;
        flex-wrap: wrap;
    }
}

/* Responsive Design */
@media (min-width: 769px) {
    .modal {
        align-items: center;
        justify-content: center;
        padding: 20px;
    }
    
    .modal-content {
        width: 600px;
        max-width: 90vw;
        margin: auto;
    }
    
    .modal-header,
    .modal-footer {
        padding: 25px 30px;
    }
    
    .modal-body {
        padding: 30px;
        max-height: calc(100vh - 200px);
    }
}

@media (max-width: 768px) {
    .motel-management {
        padding: 15px;
    }
    
    .page-header,
    .filters-section,
    .table-section {
        margin-bottom: 20px;
    }
    
    .table-container {
        display: none;
    }
    
    .mobile-cards {
        display: flex;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .btn {
        justify-content: center;
        width: 100%;
    }
    
    .motel-card-actions .btn {
        width: auto;
        flex: 1;
    }
}

@media (max-width: 480px) {
    .search-box input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .filter-group select {
        width: 100%;
    }
    
    .page-numbers {
        flex-wrap: wrap;
        justify-content: center;
    }
} 