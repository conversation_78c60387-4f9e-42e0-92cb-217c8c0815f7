/* Enhanced Dashboard Styles */
.dashboard-container {
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* Enhanced Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-title-wrapper {
    flex: 1;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-title i {
    color: #4a90a4;
    font-size: 2.2rem;
}

.page-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    margin: 0 0 15px 0;
    font-weight: 400;
}

.page-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #64748b;
}

.page-breadcrumb .separator {
    color: #cbd5e1;
}

.page-breadcrumb .current {
    color: #4a90a4;
    font-weight: 600;
}

.page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.page-actions .btn {
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-actions .btn-secondary {
    background: #e2e8f0;
    color: #475569;
}

.page-actions .btn-secondary:hover {
    background: #cbd5e1;
    transform: translateY(-2px);
}

.page-actions .btn-primary {
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    color: white;
}

.page-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 164, 0.3);
}

/* Enhanced Loading State */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    flex-direction: column;
    gap: 20px;
}

.loading-spinner {
    text-align: center;
}

.spinner-ring {
    width: 60px;
    height: 60px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #4a90a4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: #64748b;
    font-size: 1.1rem;
    margin: 0;
}

/* Enhanced Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color), var(--card-color-dark));
}

.stat-card.primary {
    --card-color: #4a90a4;
    --card-color-dark: #2c5f6f;
}

.stat-card.success {
    --card-color: #10b981;
    --card-color-dark: #059669;
}

.stat-card.info {
    --card-color: #3b82f6;
    --card-color-dark: #2563eb;
}

.stat-card.warning {
    --card-color: #f59e0b;
    --card-color-dark: #d97706;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--card-color), var(--card-color-dark));
    color: white;
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.stat-content h3 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 5px 0;
}

.stat-content p {
    font-size: 1rem;
    color: #64748b;
    margin: 0 0 10px 0;
    font-weight: 500;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85rem;
    font-weight: 600;
}

.stat-trend.positive {
    color: #10b981;
}

.stat-trend.negative {
    color: #ef4444;
}

.stat-breakdown {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.breakdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.breakdown-item .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.breakdown-item.available .dot {
    background: #10b981;
}

.breakdown-item.occupied .dot {
    background: #ef4444;
}

.stat-progress {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 10px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 4px;
    transition: width 1s ease;
}

.progress-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e293b;
    min-width: 40px;
}

.stat-chart {
    height: 40px;
    margin-top: auto;
}

/* Enhanced Analytics Section */
.analytics-section {
    margin-bottom: 30px;
}

.analytics-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.analytics-row:last-child {
    grid-template-columns: 1fr 1fr;
}

.chart-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chart-header {
    padding: 25px 25px 15px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.chart-title h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 5px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-title i {
    color: #4a90a4;
}

.chart-subtitle {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-group label {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
}

.form-control {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.85rem;
    background: white;
}

.btn-outline {
    background: transparent;
    border: 1px solid #d1d5db;
    color: #64748b;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-outline:hover {
    background: #f1f5f9;
    border-color: #4a90a4;
    color: #4a90a4;
}

.chart-body {
    padding: 20px 25px 25px;
}

.revenue-summary {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 10px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.summary-item .label {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
}

.summary-item .value {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e293b;
}

.summary-item .change {
    font-size: 0.8rem;
    font-weight: 600;
}

.summary-item .change.positive {
    color: #10b981;
}

.summary-item .change.negative {
    color: #ef4444;
}

/* Enhanced Occupancy Details */
.occupancy-details {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.detail-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.detail-icon.occupied {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.detail-icon.available {
    background: linear-gradient(135deg, #10b981, #059669);
}

.detail-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.detail-content p {
    font-size: 0.9rem;
    color: #64748b;
    margin: 2px 0;
}

.detail-content .percentage {
    font-size: 0.8rem;
    font-weight: 600;
    color: #4a90a4;
}

/* Enhanced Utility Summary */
.utility-summary {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.utility-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #64748b;
}

.utility-item.electric i {
    color: #f59e0b;
}

.utility-item.water i {
    color: #06b6d4;
}

.utility-item strong {
    color: #1e293b;
}

/* Enhanced System Grid */
.system-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.system-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 10px;
    transition: all 0.2s ease;
}

.system-item:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

.system-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.system-icon.location {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.system-icon.category {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.system-icon.banking {
    background: linear-gradient(135deg, #10b981, #059669);
}

.system-icon.message {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.system-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.system-content p {
    font-size: 0.85rem;
    color: #64748b;
    margin: 2px 0;
}

.system-content small {
    font-size: 0.75rem;
    color: #94a3b8;
}

/* Enhanced Activity Section */
.activity-section {
    margin-bottom: 30px;
}

.activity-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.activity-header {
    padding: 25px 25px 15px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-header h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.activity-header i {
    color: #4a90a4;
}

.activity-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.activity-body {
    padding: 0;
}

.activity-timeline {
    max-height: 400px;
    overflow-y: auto;
    padding: 20px 25px;
}

.timeline-item {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 22px;
    top: 45px;
    bottom: -20px;
    width: 2px;
    background: #e2e8f0;
}

.timeline-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.timeline-icon.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.timeline-icon.info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.timeline-icon.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.timeline-icon.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.timeline-content {
    flex: 1;
    padding-top: 2px;
}

.timeline-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 5px 0;
}

.timeline-content p {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0 0 8px 0;
    line-height: 1.5;
}

.timeline-meta {
    display: flex;
    align-items: center;
    gap: 15px;
}

.timeline-time {
    font-size: 0.8rem;
    color: #94a3b8;
    display: flex;
    align-items: center;
    gap: 4px;
}

.timeline-type {
    font-size: 0.75rem;
    background: #e2e8f0;
    color: #64748b;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.activity-footer {
    padding: 15px 25px 25px;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

/* Quick Actions Section */
.quick-actions-section {
    margin-bottom: 30px;
}

.quick-actions-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.actions-header {
    padding: 25px 25px 15px;
    border-bottom: 1px solid #e2e8f0;
}

.actions-header h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.actions-header i {
    color: #4a90a4;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 25px;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    text-decoration: none;
    color: #64748b;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.action-item:hover {
    background: white;
    border-color: #4a90a4;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 164, 0.1);
    color: #1e293b;
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.action-icon.contract {
    background: linear-gradient(135deg, #4a90a4, #2c5f6f);
}

.action-icon.invoice {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.action-icon.customer {
    background: linear-gradient(135deg, #10b981, #059669);
}

.action-icon.room {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.action-icon.report {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.action-icon.settings {
    background: linear-gradient(135deg, #64748b, #475569);
}

.action-item span {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .analytics-row {
        grid-template-columns: 1fr;
    }
    
    .revenue-summary {
        flex-direction: column;
        gap: 15px;
    }
    
    .occupancy-details {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 15px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }
    
    .page-actions {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .chart-controls {
        justify-content: center;
    }
    
    .system-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .utility-summary {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .timeline-item {
        gap: 10px;
    }
    
    .timeline-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .timeline-item:not(:last-child)::after {
        left: 17px;
    }
}

/* Custom Scrollbar for Activity Timeline */
.activity-timeline::-webkit-scrollbar {
    width: 6px;
}

.activity-timeline::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.activity-timeline::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.activity-timeline::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Chart Container Heights */
#revenueChart {
    height: 300px !important;
}

#occupancyChart {
    height: 250px !important;
}

#utilityChart {
    height: 250px !important;
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Customer Dashboard Specific Styles */
.info-section {
    margin-top: 30px;
}

.info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.info-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
}

.info-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-header i {
    color: #4a90a4;
    font-size: 1.3rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.info-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    color: white;
    font-size: 1.2rem;
}

.info-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 5px 0;
}

.info-content p {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
}

.utility-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.utility-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.color-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.color-indicator.primary {
    background: #4a90a4;
}

.color-indicator.success {
    background: #f59e0b;
}

.color-indicator.info {
    background: #06b6d4;
}

.color-indicator.warning {
    background: #f97316;
}

.utility-item span {
    font-size: 0.9rem;
    color: #475569;
}

.utility-item strong {
    color: #1e293b;
    font-weight: 600;
}

/* Chart specific styles for customer dashboard */
#comparisonChart, #yearComparisonChart {
    max-height: 300px;
}

/* Responsive adjustments for customer dashboard */
@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .utility-stats {
        margin-top: 15px;
    }
    
    .info-item {
        padding: 15px;
    }
    
    .info-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* No contract message styles */
.no-contract-message {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 20px;
}

.no-contract-message .btn {
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.no-contract-message .btn-primary {
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    color: white;
}

.no-contract-message .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 164, 0.3);
}

.no-contract-message .btn-secondary {
    background: #e2e8f0;
    color: #475569;
}

.no-contract-message .btn-secondary:hover {
    background: #cbd5e1;
    transform: translateY(-2px);
} 