﻿/* General Styles for the management page */
.compensation-management, .contract-management {
    font-family: 'Inter', sans-serif;
}

.page-header {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.header-left i {
    color: #007bff;
    margin-right: 10px;
}

.header-left p {
    margin: 5px 0 0;
    color: #666;
}

/* Filters */
.filters-section {
    background-color: #fff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.filters-container {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex-grow: 1;
}

    .search-box i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
    }

    .search-box input, .filter-group select {
        width: 100%;
        padding: 10px 15px 10px 40px; /* Padding for icon */
        border-radius: 8px;
        border: 1px solid #ddd;
        font-size: 1rem;
    }

.filter-group select {
    padding: 10px 15px;
    min-width: 200px;
}

.filter-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Table */
.table-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.custom-table {
    width: 100%;
    border-collapse: collapse;
}

    .custom-table th, .custom-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .custom-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #333;
    }

.btn-group .btn {
    margin: 0 2px;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info, .items-per-page {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
}

.pagination-controls {
    display: flex;
    gap: 5px;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

    .page-numbers .btn {
        min-width: 40px;
    }

        .page-numbers .btn.active {
            background-color: #007bff;
            color: #fff;
            border-color: #007bff;
        }

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1070;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    animation: slideIn 0.3s;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 25px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: white;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

    .modal-header h2 {
        font-size: 1.5rem;
        margin: 0;
    }

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: white;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

    .modal-close:hover {
        opacity: 1;
    }

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 25px;
    background-color: #f8f9fa;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.delete-modal .modal-header {
    background-color: #dc3545;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .form-group .required {
        color: #dc3545;
    }

    .form-group input, .form-group select, .form-group textarea {
        width: 100%;
        padding: 10px;
        border-radius: 8px;
        border: 1px solid #ddd;
    }

.error-message {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 5px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

.loading-content {
    text-align: center;
}

    .loading-content p {
        margin-top: 10px;
        font-weight: 500;
    }

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
    }

    to {
        transform: translateY(0);
    }
}

/* Responsive: Mobile Cards */
.mobile-cards {
    display: none;
}

@media (max-width: 768px) {
    .table-container {
        display: none;
    }

    .mobile-cards {
        display: block;
    }

    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }

    .compensation-card {
        background-color: #fff;
        border: 1px solid #eee;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

        .card-header strong {
            font-size: 1.1rem;
            flex-grow: 1;
            word-break: break-word;
        }

    .card-actions {
        flex-shrink: 0;
    }

        .card-actions .btn {
            padding: 5px 8px;
        }

    .card-body p {
        margin: 0 0 8px;
        color: #555;
    }

        .card-body p:last-child {
            margin-bottom: 0;
        }

        .card-body p strong {
            color: #333;
        }
    /* Responsive Pagination */
    .pagination-container {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }

    .pagination-info {
        flex-direction: column;
        align-items: center;
        gap: 10px;
        text-align: center;
    }

    .pagination-controls {
        width: 100%;
        display: flex;
        justify-content: center;
    }
}

.form-row {
    display: flex;
    gap: 20px;
}

    .form-row .form-group {
        flex: 1;
    }

/* CSS cho trang đền bù */

/* Border left colors cho cards thống kê */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* Card styles */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
}

/* Table styles */
.table {
    background: #23243a;
    border-radius: 12px;
    overflow: hidden;
    color: #f1f1f1;
    font-size: 1rem;
}

.table th {
    background: linear-gradient(90deg, #5a5cfa 0%, #7b7bff 100%);
    color: #fff;
    font-weight: 700;
    border-top: none;
    border-bottom: 2px solid #444;
    letter-spacing: 0.5px;
    font-size: 1.05rem;
    padding: 1rem 0.75rem;
}

.table td {
    background: #23243a;
    color: #e2e2e2;
    border-top: 1px solid #353657;
    padding: 0.85rem 0.75rem;
    vertical-align: middle;
    font-size: 1rem;
}

.table-hover tbody tr:hover {
    background: #2d2e4a;
 
    transition: background 0.2s;
}

.badge.bg-primary {
    background: linear-gradient(90deg, #5a5cfa 0%, #7b7bff 100%);
    color: #fff;
    font-weight: 600;
    border-radius: 8px;
    font-size: 0.95rem;
    padding: 0.5rem 1rem;
}

.fw-bold.text-success {
    color: #1cc88a !important;
    font-size: 1.08rem;
    letter-spacing: 0.5px;
}

.table td .text-truncate {
    max-width: 220px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table td small.text-muted {
    color: #b0b0b0 !important;
    font-size: 0.92rem;
}

.table-responsive {
    border-radius: 12px;
    overflow: auto;
    background-color: white;
}

/* Đậm border bảng */
.table, .table th, .table td {
    border-color: #353657 !important;
}

/* Responsive chỉnh lại padding nhỏ hơn trên mobile */
@media (max-width: 768px) {
    .table th, .table td {
        padding: 0.5rem 0.4rem;
        font-size: 0.95rem;
    }
    .badge.bg-primary {
        font-size: 0.85rem;
        padding: 0.35rem 0.7rem;
    }
}

/* Badge styles */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Button styles */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Pagination styles */
.pagination .page-link {
    color: #4e73df;
    border: 1px solid #e3e6f0;
}

.pagination .page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

.pagination .page-item.disabled .page-link {
    color: #858796;
    background-color: #fff;
    border-color: #e3e6f0;
}

/* Modal styles */
.modal-header {
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
}

/* Form styles */
.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
}

.form-control:focus, .form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Loading spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Text utilities */
.text-xs {
    font-size: 0.7rem;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .page-header .breadcrumb {
        margin-top: 1rem;
    }
    
    .card-body .row > div {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
    }
}

/* Animation for cards */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* Custom scrollbar */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Empty state styling */
.text-muted .fas {
    opacity: 0.5;
}

/* Status indicators */
.status-active {
    color: #1cc88a;
}

.status-pending {
    color: #f6c23e;
}

.status-completed {
    color: #4e73df;
}

/* Tooltip styles */
[title] {
    cursor: help;
}

/* Focus styles for accessibility */
.btn:focus, .form-control:focus, .form-select:focus {
    outline: 2px solid #4e73df;
    outline-offset: 2px;
}
