﻿:root {
    --primary: #4a90a4;
    --primary-light: #5ba3b8;
    --primary-dark: #3a7a8c;
    --secondary: #6b7280;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
    --light: #f8fafc;
    --dark: #1f2937;
}

body, .utility-bill-management {
    background: #f8f9fa;
}

.utility-bill-management {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-left h1 {
    color: white;
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-left p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 16px;
}

.header-right .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-right .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.filters-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.filters-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.search-box {
    position: relative;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #4a90a4;
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}

.filter-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
}

.filter-group select {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #4a90a4;
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}

#alertContainer {
    margin-bottom: 20px;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.alert-success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fed7aa;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.table-section {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.table-container {
    overflow-x: auto;
}

.bills-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.bills-table th {
    background: #f8fafc;
    padding: 16px 12px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
}

.bills-table td {
    padding: 16px 12px;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.bills-table tbody tr:hover {
    background-color: #f9fafb;
}

.bills-table tbody tr:last-child td {
    border-bottom: none;
}

.bills-table th:nth-child(2),
.bills-table td:nth-child(2) {
    min-width: 150px;
}

.bills-table th:nth-child(8),
.bills-table td:nth-child(8) {
    min-width: 120px;
}

.bills-table th:nth-child(9),
.bills-table td:nth-child(9) {
    min-width: 120px;
}

.room-info {
    display: flex;
    flex-direction: column;
}

.room-main {
    font-weight: 600;
    color: #1f2937;
}

.room-details {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
}

.text-muted {
    color: #6b7280 !important;
    font-size: 0.875rem;
}

.utility-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.utility-info span {
    font-size: 13px;
}

.amount-info {
    font-weight: 600;
    color: #059669;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
}

.status-paid {
    background-color: #dcfce7;
    color: #166534;
}

.status-unpaid {
    background-color: #fef3c7;
    color: #92400e;
}

.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    min-width: 80px;
    justify-content: center;
}

.btn-primary {
    background-color: #4a90a4;
    color: white;
}

.btn-primary:hover {
    background-color: #3d7a8a;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-success:hover {
    background-color: #059669;
    transform: translateY(-1px);
}

.btn-warning {
    background-color: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
    transform: translateY(-1px);
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
    transform: translateY(-1px);
}

.btn-outline {
    background-color: transparent;
    color: #4a90a4;
    border: 1px solid #4a90a4;
}

.btn-outline:hover {
    background-color: #4a90a4;
    color: white;
}

.btn-outline:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.mobile-cards {
    display: none;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
}

.mobile-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f3f4f6;
}

.mobile-card-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 16px;
}

.mobile-card-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.mobile-card-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 12px;
}

.mobile-card-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.mobile-card-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.mobile-card-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;
}

.mobile-card-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-top: 1px solid #e5e7eb;
    background: #f8fafc;
    flex-wrap: wrap;
    gap: 16px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-numbers {
    display: flex;
    gap: 4px;
}

.page-number {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.page-number:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.page-number.active {
    background: #4a90a4;
    color: white;
    border-color: #4a90a4;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.items-per-page label {
    margin: 0;
    font-weight: 500;
    color: #6b7280;
}

.items-per-page select {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    font-size: 14px;
    min-width: 60px;
}

.items-per-page select:focus {
    outline: none;
    border-color: #4a90a4;
    box-shadow: 0 0 0 2px rgba(74, 144, 164, 0.1);
}

.items-per-page span {
    color: #6b7280;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 99999;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow-y: auto;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 700px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    margin: auto;
    position: relative;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
    max-height: calc(90vh - 160px);
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: #f8fafc;
    flex-shrink: 0;
}

/* Remove duplicate modal CSS - this was causing conflicts */

@media (min-width: 769px) {
    .modal {
        align-items: center;
        justify-content: center;
        padding: 30px;
    }
    
    .modal-content {
        width: 700px;
        max-width: 90vw;
        max-height: 85vh;
        margin: auto;
    }
    
    .modal-body {
        max-height: calc(85vh - 160px);
    }
}

@media (max-width: 768px) {
    .modal {
        align-items: flex-start;
        justify-content: center;
        padding: 15px;
        padding-top: 20px;
        padding-bottom: 50px;
    }
    
    .modal-content {
        margin: 0 auto;
        width: 100%;
        max-width: none;
        max-height: 90vh;
    }
    
    .modal-body {
        max-height: calc(90vh - 160px);
        padding: 20px;
    }
    
    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }
    
    .modal-footer .btn {
        width: 100%;
        min-height: 50px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .modal {
        padding: 10px;
        padding-top: 15px;
        padding-bottom: 40px;
    }
    
    .modal-content {
        max-height: 92vh;
    }
    
    .modal-body {
        max-height: calc(92vh - 140px);
        padding: 16px;
    }
    
    .modal-footer .btn {
        min-height: 52px;
        font-weight: 700;
    }
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.required {
    color: #ef4444;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4a90a4;
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
    display: none;
}

.error-message.show {
    display: block;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 32px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #4a90a4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    margin: 0;
    color: #6b7280;
    font-weight: 500;
}

.print-modal .modal-content {
    width: 850px;
    max-width: 95vw;
    max-height: 90vh;
}

.print-modal .modal-body {
    padding: 30px;
    background: white;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

.bill-print-content {
    padding: 24px;
    background: white;
}

.bill-header {
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e5e7eb;
}

.bill-title {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.bill-subtitle {
    color: #6b7280;
    font-size: 16px;
}

.bill-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.bill-info-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 8px;
}

.bill-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.bill-info-label {
    color: #6b7280;
    font-weight: 500;
}

.bill-info-value {
    color: #1f2937;
    font-weight: 600;
}

.bill-total {
    text-align: right;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 2px solid #e5e7eb;
}

.bill-total-amount {
    font-size: 20px;
    font-weight: 700;
    color: #059669;
}

@media (max-width: 768px) {
    .utility-bill-management {
        padding: 16px;
    }
    
    .page-header {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .header-left h1 {
        font-size: 24px;
    }
    
    .header-right .btn {
        width: 100%;
        justify-content: center;
    }
    
    .filters-section {
        padding: 16px;
        margin-bottom: 20px;
    }
    
    .search-box {
        max-width: none;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group select {
        min-width: auto;
    }
    
    .bills-table {
        display: none;
    }
    
    .mobile-cards {
        display: flex;
    }
    
    .mobile-card-content {
        grid-template-columns: 1fr;
    }
    
    .mobile-card-actions {
        justify-content: center;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 12px;
    }
    
    .pagination-controls {
        justify-content: center;
    }
    
    .modal {
        padding: 16px;
    }
    
    .modal-content {
        max-height: 85vh;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-footer {
        padding: 16px 20px;
        flex-direction: column;
    }
    
    .modal-footer .btn {
        width: 100%;
    }
    
    .bill-info {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-row .form-group {
        margin-bottom: 20px;
    }
    
    .pagination-info {
        justify-content: center;
        gap: 15px;
    }
    
    .items-per-page {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .utility-bill-management {
        padding: 12px;
    }
    
    .page-header {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .header-left h1 {
        font-size: 20px;
    }
    
    .filters-section {
        padding: 12px;
        margin-bottom: 16px;
    }
    
    .modal {
        padding: 12px;
    }
    
    .modal-content {
        max-height: 90vh;
    }
    
    .modal-body {
        padding: 16px;
    }
    
    .modal-footer {
        padding: 12px 16px;
    }
    
    .btn {
        padding: 10px 16px;
        font-size: 14px;
        min-height: 44px;
    }
}

/* CSS cho hóa đơn in đẹp */
.bill-print-beautiful {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.bill-header-beautiful {
    text-align: center;
    border-bottom: 2px solid #333;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.company-name {
    font-size: 24px;
    font-weight: bold;
    margin: 0 0 10px 0;
    color: #333;
}

.company-address, .company-contact, .company-website {
    margin: 5px 0;
    font-size: 14px;
    color: #666;
}

.bill-title-beautiful h2 {
    font-size: 20px;
    font-weight: bold;
    margin: 15px 0 10px 0;
    text-transform: uppercase;
    color: #333;
}

.bill-number, .bill-date {
    margin: 5px 0;
    font-weight: bold;
    color: #333;
}

.customer-info-beautiful {
    margin-bottom: 20px;
}

.info-section {
    margin-bottom: 15px;
}

.info-section h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
    color: #333;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
}

.label {
    font-weight: bold;
    min-width: 120px;
    color: #333;
}

.value {
    color: #666;
}

.bill-details-beautiful h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
    color: #333;
}

.bill-table-beautiful {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.bill-table-beautiful th,
.bill-table-beautiful td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.bill-table-beautiful th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.bill-table-beautiful td:first-child {
    text-align: left;
}

.bill-total-beautiful {
    text-align: right;
    margin-bottom: 20px;
}

.total-row {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.total-amount {
    font-size: 20px;
    color: #333;
}

.payment-status {
    margin-top: 10px;
}

.status-value.paid {
    color: #28a745;
    font-weight: bold;
}

.status-value.unpaid {
    color: #dc3545;
    font-weight: bold;
}

.bill-footer-beautiful {
    margin-top: 30px;
}

.signature-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.signature-box {
    text-align: center;
    width: 45%;
}

.signature-line {
    border-top: 1px solid #333;
    margin-top: 40px;
}

.note-section {
    border-top: 1px solid #333;
    padding-top: 15px;
}

.note-section ul {
    margin: 10px 0;
    padding-left: 20px;
}

.note-section li {
    margin-bottom: 5px;
    color: #666;
}
