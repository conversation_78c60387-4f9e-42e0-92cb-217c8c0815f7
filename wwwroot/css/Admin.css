﻿/* Modern Admin Dashboard Styles */
:root {
    --primary: #4a90a4;
    --primary-light: #5aa3b8;
    --primary-dark: #2c5f6f;
    --secondary: #64748b;
    --accent: #f59e0b;
    --success: #10b981;
    --danger: #ef4444;
    --warning: #f59e0b;
    --info: #06b6d4;
    --light: #f8fafc;
    --white: #ffffff;
    --dark: #1e293b;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 75px;
    --transition-speed: 0.3s;
    --border-radius: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--gray-800);
    font-size: 14px;
    line-height: 1.6;
    overflow-x: hidden;
}

.list-unstyled {
    list-style: none;
    padding-left: 0;
}

.container-fluid {
    width: 100%;
    padding-right: 20px;
    padding-left: 20px;
    margin-right: auto;
    margin-left: auto;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Layout Structure */
.wrapper {
    display: flex;
    width: 100%;
    min-height: 100vh;
    align-items: stretch;
}

/* Modern Sidebar */
#sidebar {
    width: var(--sidebar-width);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    color: var(--white);
    transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    overflow-y: auto;
}

#sidebar:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    opacity: 0.3;
}

    #sidebar.active {
        width: var(--sidebar-collapsed-width);
    }

/* Sidebar Header */
    #sidebar .sidebar-header {
    padding: 25px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    }

#sidebar .sidebar-header h1 {
            display: block;
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
        }

        #sidebar .sidebar-header strong {
            display: none;
    font-size: 1.75rem;
    color: #5aa3b8;
        }

#sidebar.active .sidebar-header h1 {
        display: none;
    }

    #sidebar.active .sidebar-header strong {
        display: block;
    }

/* Mobile Close Button */
.btn-close-mobile {
    display: none;
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    font-size: 1.1rem;
    padding: 10px 12px;
    border-radius: 8px;
    cursor: pointer;
        transition: all var(--transition-speed);
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    backdrop-filter: blur(10px);
        }

.btn-close-mobile:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.5);
    color: #ffffff;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-close-mobile:active {
    transform: translateY(-50%) scale(0.95);
        }

/* Sidebar overlay for mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
        opacity: 0;
    visibility: hidden;
    transition: all var(--transition-speed);
    backdrop-filter: blur(4px);
    }

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
        }

/* Admin Profile */
.admin-profile {
    display: flex;
    padding: 25px 20px;
    align-items: center;
    gap: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    transition: all var(--transition-speed);
}

.admin-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
    position: relative;
}

.admin-avatar:before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.5;
}

.admin-info {
    overflow: hidden;
    transition: all var(--transition-speed);
}

.admin-info h2 {
    font-size: 1.1rem;
    margin: 0 0 5px 0;
        white-space: nowrap;
    font-weight: 600;
    }

    .admin-info p {
    font-size: 0.85rem;
        margin: 0;
    opacity: 0.8;
        white-space: nowrap;
    }

#sidebar.active .admin-info {
    opacity: 0;
    width: 0;
    display: none;
}

/* Override for mobile - always show admin info when sidebar is open */
@media (max-width: 992px) {
    #sidebar.active .admin-info {
        opacity: 1 !important;
        width: auto !important;
        display: flex !important;
    }
}

/* Menu Scroll Container */
.menu-scroll-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px 0;
    margin: 10px 0;
}

.menu-scroll-container::-webkit-scrollbar {
    width: 4px;
}

.menu-scroll-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin: 10px 0;
}

.menu-scroll-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
}

.menu-scroll-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Menu Styles */
#sidebar ul.components {
    padding: 0;
    margin: 0;
}

#sidebar ul li {
    margin: 0;
}

/* Menu Sections */
.menu-section {
    padding: 20px 15px 10px 15px !important;
    border-bottom: none !important;
}

.menu-section:first-of-type {
    padding-top: 10px !important;
}

.menu-section-title {
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.6);
    display: block;
    position: relative;
    padding-left: 10px;
}

.menu-section-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 12px;
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    border-radius: 2px;
}

/* Menu Icon Wrapper */
.menu-icon-wrapper {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    transition: all var(--transition-speed);
    flex-shrink: 0;
}

#sidebar ul li a {
    text-align: left;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    color: rgba(255, 255, 255, 0.85);
    text-decoration: none;
    transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin: 3px 10px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.9rem;
}

#sidebar ul li a:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
    border-radius: var(--border-radius);
    transition: width var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
}

#sidebar ul li a .menu-icon-wrapper i {
    font-size: 16px;
    color: inherit;
    transition: all var(--transition-speed);
}

#sidebar ul li a span {
    white-space: nowrap;
    opacity: 1;
    transition: all var(--transition-speed);
    font-weight: 500;
}

#sidebar.active ul li a span {
    opacity: 0;
    display: none;
}

/* Override for mobile - always show text when sidebar is open */
@media (max-width: 992px) {
    #sidebar.active ul li a span {
        opacity: 1 !important;
        display: inline !important;
    }
    
    .menu-scroll-container {
        max-height: calc(100vh - 200px);
    }
    
    .menu-section-title {
        font-size: 0.7rem;
    }
    
    #sidebar ul li a {
        padding: 10px 12px;
        margin: 2px 8px;
    }
    
    .menu-icon-wrapper {
        width: 35px;
        height: 35px;
    }
}

#sidebar ul li a:hover {
    color: var(--white);
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(3px);
}

#sidebar ul li a:hover:before {
    width: 100%;
}

#sidebar ul li a:hover .menu-icon-wrapper {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

#sidebar ul li a:hover .menu-icon-wrapper i {
    transform: scale(1.1);
    color: var(--white);
}

#sidebar ul li.active > a {
    color: var(--white);
    background: linear-gradient(135deg, rgba(74, 144, 164, 0.3) 0%, rgba(90, 163, 184, 0.15) 100%);
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(74, 144, 164, 0.2);
    border: 1px solid rgba(74, 144, 164, 0.4);
}

#sidebar ul li.active > a:before {
    width: 100%;
}

#sidebar ul li.active > a .menu-icon-wrapper {
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    box-shadow: 0 2px 8px rgba(74, 144, 164, 0.3);
}

#sidebar ul li.active > a .menu-icon-wrapper i {
    color: var(--white);
    transform: scale(1.1);
}

/* Sidebar Footer */
.sidebar-footer {
    margin-top: auto;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    background: rgba(0, 0, 0, 0.1);
}

/* Content Area */
#content {
    width: calc(100% - var(--sidebar-width));
    min-height: 100vh;
    transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
    position: absolute;
    top: 0;
    right: 0;
    background: var(--light);
}

    #content.active {
        width: calc(100% - var(--sidebar-collapsed-width));
    }

/* Modern Navbar */
.navbar {
    padding: 0 25px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: none;
    border-radius: 0;
    margin-bottom: 30px;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    height: var(--header-height);
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid var(--gray-200);
}

#sidebarCollapse {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    border: none;
    font-size: 1.1rem;
    color: var(--white);
    cursor: pointer;
    transition: all var(--transition-speed);
    padding: 10px 12px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    min-width: 44px;
    flex-shrink: 0;
}

    #sidebarCollapse:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
}

#sidebarCollapse:active {
    transform: translateY(0);
    }

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Button Styles */
.btn {
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    }

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
    color: var(--white);
    box-shadow: var(--shadow);
    }

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Enhanced Form Styles */
.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.form-control {
    width: 100%;
    padding: 16px 20px;
    border-radius: var(--border-radius);
    border: 2px solid var(--gray-200);
    font-size: 1rem;
    transition: all var(--transition-speed);
    background: var(--white);
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: var(--gray-400);
}

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 35px;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 30px;
    border-radius: var(--border-radius);
    background: var(--white);
    box-shadow: var(--shadow);
    transition: all var(--transition-speed);
    border: 1px solid var(--gray-100);
    position: relative;
    overflow: hidden;
}

.stat-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
}

    .stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    }

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    margin-right: 25px;
    position: relative;
}

    .stat-icon.primary {
    background: linear-gradient(135deg, rgba(74, 144, 164, 0.1) 0%, rgba(90, 163, 184, 0.05) 100%);
        color: var(--primary);
    }

    .stat-icon.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
        color: var(--success);
    }

    .stat-icon.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
        color: var(--warning);
    }

    .stat-icon.info {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(8, 145, 178, 0.05) 100%);
        color: var(--info);
    }

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 8px 0;
}

.stat-content p {
    color: var(--gray-600);
    margin: 0;
    font-weight: 500;
    }

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --light: #111827;
        --gray-800: #f9fafb;
        --gray-700: #e5e7eb;
        --gray-600: #d1d5db;
        --gray-200: #374151;
        --gray-100: #4b5563;
    }
    }

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 4px 12px;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.badge-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(74, 144, 164, 0.3);
    }

.badge-secondary {
    background: linear-gradient(135deg, var(--secondary) 0%, var(--gray-600) 100%);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(100, 116, 139, 0.3);
    }

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-speed);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    }

.card-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    border-bottom: 1px solid var(--gray-200);
    padding: 20px;
    font-weight: 600;
    }

.card-body {
    padding: 25px;
    }

/* Responsive Design */
@media (max-width: 992px) {
    #sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
    }
    
    #sidebar.active {
        transform: translateX(0);
        width: var(--sidebar-width);
    }

    /* Show close button on mobile when sidebar is active */
    #sidebar.active .btn-close-mobile {
        display: block;
    }
    
    /* Always show full menu items on mobile */
    #sidebar .sidebar-header h1 {
        display: block !important;
        }

        #sidebar .sidebar-header strong {
        display: none !important;
        }

        #sidebar ul li a span {
        opacity: 1 !important;
        display: inline !important;
        }

    .admin-info {
        opacity: 1 !important;
        width: auto !important;
        display: flex !important;
    }

    #content {
        width: 100%;
        margin-left: 0;
    }
    
    #content.active {
        width: 100%;
    }
    
    /* Add overlay when sidebar is open on mobile */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-speed);
    }

    body.sidebar-open::after {
        opacity: 1;
        visibility: visible;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px;
    }
    
    .navbar {
        padding: 0 15px;
        margin-bottom: 20px;
    }
    
    #sidebarCollapse {
        width: 40px;
        height: 40px;
        padding: 8px;
        font-size: 1rem;
        min-width: 40px;
    }
    
    #sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

        #sidebar.active {
        transform: translateX(0);
        width: 100%;
    }
    
    /* Show close button on mobile */
    #sidebar.active .btn-close-mobile {
        display: block;
        }

    #content {
        width: 100%;
    }

        #content.active {
            width: 100%;
        }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
    }

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    }

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.transition-all {
    transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
}
