/* Chatbot Styles */
.chatbot-container {
    display: flex;
    height: calc(100vh - 120px);
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 20px;
}

/* Sidebar */
.chat-sidebar {
    width: 300px;
    background: white;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.sidebar-header h5 {
    margin: 0 0 15px 0;
    color: #333;
    font-weight: 600;
}



#searchConversation {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
}

.conversation-list {
    list-style: none;
    margin: 0;
    padding: 0;
    flex: 1;
    overflow-y: auto;
}

.conversation-list li {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.conversation-list li:hover {
    background: #f8f9fa;
}

.conversation-list li.active {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.conversation-list li.loading {
    text-align: center;
    color: #666;
    font-style: italic;
}

.conversation-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #2196f3;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-last-message {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
}

/* Unread badge */
.unread-badge {
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    margin-left: auto;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(255, 71, 87, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
    }
}

/* Main Chat Area */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-partner {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    scroll-behavior: smooth;
}

.empty-chat {
    text-align: center;
    color: #666;
    font-style: italic;
    margin-top: 100px;
}

.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-end;
    gap: 8px;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.message.sent {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #2196f3;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
    flex-shrink: 0;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
    animation: messageSlideIn 0.3s ease-out;
}

.message.received .message-content {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
}

.message.sent .message-content {
    background: #2196f3;
    color: white;
}

.message-sender {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #666;
}

.message.received .message-sender {
    color: #333;
}

.message.sent .message-sender {
    color: rgba(255, 255, 255, 0.9);
    text-align: right;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
    text-align: right;
}

.message.sent .message-time {
    text-align: left;
}

.chat-input-area {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    background: white;
}

#chatForm {
    display: flex;
    gap: 12px;
    align-items: center;
}

#chatInput {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

#chatInput:focus {
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

#sendBtn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2196f3;
    border: none;
    color: white;
    font-size: 18px;
    transition: all 0.2s ease;
}

#sendBtn:hover {
    background: #1976d2;
    transform: scale(1.05);
}

#sendBtn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Loading Animation */
.typing-indicator {
    display: flex;
    gap: 4px;
    padding: 12px 16px;
    background: white;
    border-radius: 18px;
    border: 1px solid #e9ecef;
    width: fit-content;
    opacity: 0.7;
    animation: typingFadeIn 0.3s ease-out;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #999;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes typingFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 0.7;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatbot-container {
        flex-direction: column;
        height: calc(100vh - 80px);
        margin: 10px;
    }
    
    .chat-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }
    
    .conversation-list li {
        padding: 12px 15px;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .chat-messages {
        padding: 15px;
    }
    
    .chat-input-area {
        padding: 15px;
    }
}

/* Scrollbar Styling */
.conversation-list::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.conversation-list::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.conversation-list::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.conversation-list::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for new messages */
.message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status indicators */
.online-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #4caf50;
    border: 2px solid white;
    position: absolute;
    bottom: 0;
    right: 0;
}

.offline-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ccc;
    border: 2px solid white;
    position: absolute;
    bottom: 0;
    right: 0;
}

/* Message content animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Smooth transitions for all interactive elements */
.conversation-list li,
.message,
.chat-input-area,
#sendBtn {
    transition: all 0.2s ease;
}

/* Loading state for messages */
.message.sending {
    opacity: 0.7;
}

.message.sending::after {
    content: '';
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid #ddd;
    border-top: 2px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
} 