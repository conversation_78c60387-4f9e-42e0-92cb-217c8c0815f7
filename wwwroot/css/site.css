﻿/* ===== CSS VARIABLES - BẢNG MÀU CÀI TIẾN ===== */
:root {
    /* <PERSON><PERSON><PERSON> ch<PERSON>h - <PERSON><PERSON>h dương nhẹ nhàng và hài hòa */
    --primary-gradient: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    --secondary-gradient: linear-gradient(135deg, #23272f 0%, #090f18 100%);
    /* <PERSON><PERSON><PERSON> nhấn - <PERSON> ấm áp thay vì đỏ-vàng chói */
    --accent-gradient: linear-gradient(45deg, #ed8936, #f6ad55);
    /* <PERSON><PERSON>u chức năng - Điều chỉnh để hài hòa hơn */
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #f56565;
    --info-color: #4299e1;
    /* <PERSON><PERSON><PERSON> nền - <PERSON><PERSON> tế hơn */
    --light-color: #f7fafc;
    --dark-color: #2d3748;
    /* <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> t<PERSON>h kh<PERSON>c g<PERSON><PERSON> nguyên */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --shadow-light: 0 4px 20px rgba(0,0,0,0.08);
    --shadow-medium: 0 8px 30px rgba(0,0,0,0.12);
    --shadow-heavy: 0 15px 50px rgba(0,0,0,0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* ===== RESET & BASE STYLES ===== */
* {
    box-sizing: border-box;
}

html {
    font-size: 14px;
    scroll-behavior: smooth;
}

@media (min-width: 768px) {
    html {
        font-size: 16px;
    }
}

body {
    font-family: var(--font-family);
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* ===== ACCESSIBILITY ===== */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--dark-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
}

    .skip-link:focus {
        top: 6px;
    }

/* ===== FOCUS STYLES - Cập nhật màu focus ===== */
.btn:focus,
.btn:active:focus,
.btn-link.nav-link:focus,
.form-control:focus,
.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 164, 0.25);
    outline: none;
}

/* ===== NAVBAR STYLES ===== */
.custom-navbar {
    background: var(--primary-gradient);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-medium);
    padding: 1rem 0;
    transition: var(--transition);
    z-index: 1030;
}

    .custom-navbar.scrolled {
        padding: 0.5rem 0;
        box-shadow: var(--shadow-heavy);
    }

.navbar-brand img {
    height: 45px;
    width: auto;
    transition: var(--transition);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.navbar-brand:hover img {
    transform: scale(1.05) rotate(1deg);
}

.nav-link {
    color: white !important;
    font-weight: 500;
    padding: 0.75rem 1.25rem !important;
    margin: 0 0.25rem;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

    .nav-link::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: var(--accent-gradient);
        transition: var(--transition);
        transform: translateX(-50%);
        border-radius: 2px;
    }

    .nav-link:hover {
        background-color: rgba(255,255,255,0.15);
        color: #f8f9fa !important;
        transform: translateY(-2px);
    }

        .nav-link:hover::before {
            width: 80%;
        }

.navbar-spacer {
    height: 85px;
}

/* ===== USER DROPDOWN ===== */
.user-dropdown {
    position: relative;
}

    .user-dropdown .nav-link {
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        text-transform: none;
        letter-spacing: normal;
    }

        .user-dropdown .nav-link:hover {
            background: rgba(255,255,255,0.25);
        }

.dropdown-menu {
    background: white;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    min-width: 220px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    color: var(--dark-color);
    font-weight: 500;
    transition: var(--transition);
    border-radius: 0;
    display: flex;
    align-items: center;
}

    .dropdown-item:hover {
        background: var(--primary-gradient);
        color: white;
        transform: translateX(5px);
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-right: 0.5rem;
    }

/* ===== BUTTONS ===== */
.btn {
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.75rem 2rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

.btn-primary {
    background: var(--primary-gradient);
    border: none;
    box-shadow: var(--shadow-light);
}

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }

.btn-outline-primary {
    border: 2px solid #4a90a4;
    color: #4a90a4;
    background: transparent;
}

    .btn-outline-primary:hover {
        background: var(--primary-gradient);
        border-color: transparent;
        color: white;
        transform: translateY(-2px);
    }

/* Thêm các button styles khác */
.btn-secondary {
    background: var(--secondary-gradient);
    border: none;
    color: white;
}

.btn-accent {
    background: var(--accent-gradient);
    border: none;
    color: white;
}

.btn-success {
    background: var(--success-color);
    border: none;
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    border: none;
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    border: none;
    color: white;
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

    .card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-heavy);
    }

.card-img-top {
    transition: var(--transition);
    height: 250px;
    object-fit: cover;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.card-text {
    color: #718096;
    line-height: 1.6;
}

/* ===== ROOM CARDS ===== */
.room-card {
    position: relative;
    margin-bottom: 2rem;
}

    .room-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0;
        transition: var(--transition);
        border-radius: var(--border-radius);
        z-index: 1;
    }

    .room-card:hover::before {
        opacity: 0.1;
    }

    .room-card .card-body {
        position: relative;
        z-index: 2;
    }

    .room-card .price {
        background: var(--accent-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 800;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .room-card .location {
        color: #718096;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

        .room-card .location i {
            margin-right: 0.5rem;
            color: var(--info-color);
        }

.room-features {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.room-feature {
    text-align: center;
    font-size: 0.85rem;
    color: #718096;
}

    .room-feature i {
        display: block;
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
        color: var(--info-color);
    }

/* ===== FOOTER - Cập nhật màu ===== */
.footer {
    background: var(--secondary-gradient);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-section {
    margin-bottom: 2rem;
}

.footer-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-subtitle {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #e2e8f0;
}

.footer-description {
    color: #cbd5e0;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-address {
    font-style: normal;
}

.address-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: #cbd5e0;
}

    .address-item i {
        width: 20px;
        margin-right: 0.75rem;
        color: var(--info-color);
    }

    .address-item a {
        color: #cbd5e0;
        text-decoration: none;
        transition: var(--transition);
    }

        .address-item a:hover {
            color: white;
        }

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

    .footer-links li {
        margin-bottom: 0.5rem;
    }

    .footer-links a {
        color: #cbd5e0;
        text-decoration: none;
        transition: var(--transition);
        display: inline-block;
    }

        .footer-links a:hover {
            color: white;
            transform: translateX(5px);
        }

/* ===== ALERT COMPONENTS ===== */
.alert {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: rgba(72, 187, 120, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-warning {
    background-color: rgba(237, 137, 54, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-danger {
    background-color: rgba(245, 101, 101, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-info {
    background-color: rgba(66, 153, 225, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* ===== FORM ELEMENTS ===== */
.form-control {
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background-color: white;
}

    .form-control:focus {
        border-color: #4a90a4;
        box-shadow: 0 0 0 0.2rem rgba(74, 144, 164, 0.25);
    }

/* ===== BADGES ===== */
.badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
}

.badge-primary {
    background: var(--primary-gradient);
    color: white;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

/* ===== PROGRESS BARS ===== */
.progress {
    height: 10px;
    background-color: #e2e8f0;
    border-radius: 20px;
    overflow: hidden;
}

.progress-bar {
    background: var(--primary-gradient);
    transition: width 0.6s ease;
}

    .progress-bar.bg-success {
        background: var(--success-color);
    }

    .progress-bar.bg-warning {
        background: var(--warning-color);
    }

    .progress-bar.bg-danger {
        background: var(--danger-color);
    }

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
    .btn {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }

    .card-body {
        padding: 1rem;
    }

    .footer {
        padding: 2rem 0 1rem;
    }
}

/* ===== ANIMATION UTILITIES ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== NEWSLETTER FORM (tiếp tục từ phần bị cắt) ===== */
.newsletter {
    background: var(--primary-gradient);
    padding: 3rem 0;
    color: white;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    gap: 1rem;
}

.newsletter-input {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
}

.newsletter-btn {
    background: var(--accent-gradient);
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

    .newsletter-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }
