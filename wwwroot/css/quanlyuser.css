/* QuanlyUser - Style tách bi<PERSON>t, không ảnh hưởng layout khác */

.quanlyuser-container {
    --primary: #4a90a4;
    --secondary: #64748b;
    --danger: #ef4444;
    --accent: #f59e0b;
    --info: #0ea5e9;
    --bg: #f8fafc;
    --text: #222;
    background: var(--bg);
    min-height: 100vh;
    padding: 24px 0;
}

/* Header */
.quanlyuser-container .page-header { background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #e5e7eb; padding: 24px; margin-bottom: 24px; }
.quanlyuser-container .header-content { display: flex; justify-content: space-between; align-items: center; }
.quanlyuser-container .header-left h1 { font-size: 2rem; font-weight: 700; color: var(--primary); margin: 0; display: flex; align-items: center; gap: 12px; }
.quanlyuser-container .header-left p { color: var(--secondary); margin: 4px 0 0 0; }

/* Filter, Search */
.quanlyuser-container .filters-section { background: #fff; border-radius: 8px; box-shadow: 0 1px 4px #e5e7eb; padding: 16px 24px; margin-bottom: 20px; }
.quanlyuser-container .filters-container { display: flex; gap: 16px; align-items: center; flex-wrap: wrap; }
.quanlyuser-container .search-box { 
    position: relative; 
}
.quanlyuser-container .search-box i { 
    position: absolute; 
    left: 12px; 
    top: 50%; 
    transform: translateY(-50%); 
    color: #94a3b8; 
}
.quanlyuser-container .search-box input { 
    padding: 8px 8px 8px 36px; 
    border: 1px solid #e5e7eb; 
    border-radius: 6px; 
    min-width: 220px; 
    font-size: 0.95rem;
    transition: border-color 0.2s;
}
.quanlyuser-container .search-box input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}
.quanlyuser-container .filter-group { 
    display: flex; 
    gap: 12px; 
    align-items: center; 
}
.quanlyuser-container .form-select { 
    padding: 8px 12px; 
    border: 1px solid #e5e7eb; 
    border-radius: 6px; 
    font-size: 0.95rem;
    transition: border-color 0.2s;
}
.quanlyuser-container .form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}

/* Table */
.quanlyuser-container .table-section { 
    background: #fff; 
    border-radius: 8px; 
    box-shadow: 0 1px 4px #e5e7eb; 
    padding: 0; 
}
.quanlyuser-container .table-container { 
    width: 100%; 
    overflow-x: auto; 
}
.quanlyuser-container .table-responsive { 
    width: 100%; 
}
.quanlyuser-container .users-table { 
    width: 100%; 
    border-collapse: collapse; 
}
.quanlyuser-container .users-table th, 
.quanlyuser-container .users-table td { 
    padding: 12px 10px; 
    border-bottom: 1px solid #e5e7eb; 
    text-align: left; 
}
.quanlyuser-container .users-table th { 
    background: #f1f5f9; 
    color: var(--secondary); 
    font-weight: 600; 
    text-transform: uppercase; 
    font-size: 0.95rem; 
}
.quanlyuser-container .users-table tbody tr:hover { 
    background: #f8fafc; 
}

/* Table responsive for mobile */
@media (max-width: 768px) {
    .quanlyuser-container .users-table th,
    .quanlyuser-container .users-table td {
        padding: 8px 6px;
        font-size: 0.9rem;
    }
    
    .quanlyuser-container .users-table th {
        font-size: 0.85rem;
    }
}

/* Badge, Button */
.quanlyuser-container .btn { padding: 8px 18px; border-radius: 6px; border: none; font-weight: 600; cursor: pointer; transition: 0.2s; display: inline-flex; align-items: center; gap: 8px; }
.quanlyuser-container .btn-primary { background: var(--primary); color: #fff; }
.quanlyuser-container .btn-secondary { background: var(--secondary); color: #fff; }
.quanlyuser-container .btn-danger { background: var(--danger); color: #fff; }
.quanlyuser-container .btn-outline { background: #fff; color: var(--primary); border: 1px solid var(--primary); }
.quanlyuser-container .btn:disabled { opacity: 0.6; cursor: not-allowed; }

.quanlyuser-container .role-badge { padding: 4px 12px; border-radius: 12px; font-size: 0.9em; font-weight: 600; display: inline-block; text-transform: uppercase; }
.quanlyuser-container .role-badge.admin { background: #ef4444; color: #fff; }
.quanlyuser-container .role-badge.manager { background: #f59e42; color: #fff; }
.quanlyuser-container .role-badge.customer { background: #22c55e; color: #fff; }

.quanlyuser-container .status-badge { padding: 4px 12px; border-radius: 12px; font-size: 0.9em; font-weight: 600; display: inline-block; }
.quanlyuser-container .status-badge.active { background: #22c55e; color: #fff; }
.quanlyuser-container .status-badge.inactive { background: #64748b; color: #fff; }

/* Modal */
.modal { 
    display: none !important; 
    position: fixed !important; 
    z-index: 999999 !important; 
    left: 0 !important; 
    top: 0 !important; 
    width: 100vw !important; 
    height: 100vh !important; 
    background: rgba(30,41,59,0.25) !important; 
    align-items: center !important; 
    justify-content: center !important; 
    transition: opacity 0.2s !important; 
}
.modal.show { 
    display: flex !important; 
    opacity: 1 !important;
    visibility: visible !important;
}
.modal-content { 
    background: #fff; 
    border-radius: 14px; 
    box-shadow: 0 8px 32px #4a90a455; 
    width: 100%; 
    max-width: 420px; 
    padding: 0; 
    display: flex; 
    flex-direction: column; 
    animation: modalFadeIn 0.25s; 
    max-height: 90vh;
    overflow-y: auto;
}
.modal-header { 
    padding: 20px 24px; 
    border-bottom: 1px solid #e5e7eb; 
    display: flex; 
    align-items: center; 
    justify-content: space-between; 
    flex-shrink: 0;
}
.modal-body { 
    padding: 20px 24px; 
    flex: 1;
    overflow-y: auto;
}
.modal-footer { 
    padding: 16px 24px; 
    border-top: 1px solid #e5e7eb; 
    display: flex; 
    gap: 12px; 
    justify-content: flex-end; 
    flex-shrink: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s;
}

.modal-close:hover {
    color: #ef4444;
}

/* Form */
.quanlyuser-container .form-group { 
    margin-bottom: 16px; 
}
.quanlyuser-container .form-group label { 
    font-weight: 600; 
    margin-bottom: 4px; 
    display: block; 
    color: var(--text);
}
.quanlyuser-container .form-group input, 
.quanlyuser-container .form-group select { 
    width: 100%; 
    padding: 8px 12px; 
    border: 1px solid #e5e7eb; 
    border-radius: 6px; 
    font-size: 0.95rem;
    transition: border-color 0.2s;
}
.quanlyuser-container .form-group input:focus,
.quanlyuser-container .form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}
.quanlyuser-container .form-group .error-message { 
    color: var(--danger); 
    font-size: 0.9em; 
    margin-top: 2px; 
    display: none;
}
.quanlyuser-container .form-group .error-message.show {
    display: block;
}
.quanlyuser-container .form-group .form-hint {
    color: #64748b;
    font-size: 0.85em;
    margin-top: 2px;
    font-style: italic;
}
.quanlyuser-container .required {
    color: var(--danger);
}

/* Pagination */
.quanlyuser-container .pagination-container { 
    display: flex; 
    justify-content: space-between; 
    align-items: center; 
    padding: 16px 24px; 
    border-top: 1px solid #e5e7eb; 
    background: #fff; 
    border-radius: 0 0 8px 8px; 
}
.quanlyuser-container .pagination-info { 
    color: var(--secondary); 
    font-size: 0.95em; 
    display: flex; 
    align-items: center; 
    gap: 16px; 
}
.quanlyuser-container .items-per-page { 
    display: flex; 
    align-items: center; 
    gap: 8px; 
}
.quanlyuser-container .pagination-controls { 
    display: flex; 
    gap: 6px; 
    align-items: center;
}
.quanlyuser-container .page-numbers {
    display: flex;
    gap: 4px;
    align-items: center;
}
.quanlyuser-container .pagination-ellipsis {
    color: var(--secondary);
    padding: 0 8px;
}

/* Toast, Loading */
.loading-overlay { 
    position: fixed; 
    left: 0; 
    top: 0; 
    width: 100vw; 
    height: 100vh; 
    background: rgba(255,255,255,0.7); 
    z-index: 100000; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
}
.loading-content { 
    background: #fff; 
    border-radius: 8px; 
    padding: 32px 40px; 
    box-shadow: 0 2px 12px #0001; 
    display: flex; 
    flex-direction: column; 
    align-items: center; 
}
.spinner { 
    width: 32px; 
    height: 32px; 
    border: 4px solid #e5e7eb; 
    border-top: 4px solid var(--primary); 
    border-radius: 50%; 
    animation: quanlyuser-spin 1s linear infinite; 
    margin-bottom: 12px; 
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100001;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.toast {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    max-width: 400px;
    animation: toastSlideIn 0.3s ease-out;
    border-left: 4px solid;
}

.toast.success {
    border-left-color: #22c55e;
}

.toast.error {
    border-left-color: #ef4444;
}

.toast.warning {
    border-left-color: #f59e0b;
}

.toast.info {
    border-left-color: #0ea5e9;
}

.toast-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.toast.success .toast-icon {
    color: #22c55e;
}

.toast.error .toast-icon {
    color: #ef4444;
}

.toast.warning .toast-icon {
    color: #f59e0b;
}

.toast.info .toast-icon {
    color: #0ea5e9;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 2px;
    color: var(--text);
}

.toast-message {
    font-size: 0.9rem;
    color: var(--secondary);
}

.toast-close {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s;
}

.toast-close:hover {
    color: #ef4444;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes quanlyuser-spin { 
    100% { 
        transform: rotate(360deg); 
    } 
}

/* Mobile Cards */
.quanlyuser-container .mobile-cards {
    display: none;
}

@media (max-width: 768px) {
    .quanlyuser-container .page-header, 
    .quanlyuser-container .filters-section, 
    .quanlyuser-container .table-section { 
        padding: 12px; 
    }
    .quanlyuser-container .modal-content { 
        max-width: 98vw; 
    }
    .quanlyuser-container .modal-header, 
    .quanlyuser-container .modal-body, 
    .quanlyuser-container .modal-footer { 
        padding: 12px; 
    }
    .quanlyuser-container .pagination-container { 
        flex-direction: column; 
        gap: 8px; 
        align-items: flex-start; 
    }
    
    .quanlyuser-container .table-responsive {
        display: none;
    }
    
    .quanlyuser-container .mobile-cards {
        display: block;
    }
    
    .quanlyuser-container .user-card {
        background: #fff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 1px 4px #e5e7eb;
    }
    
    .quanlyuser-container .user-card-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
    }
    
    .quanlyuser-container .user-card-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: var(--primary);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.2rem;
    }
    
    .quanlyuser-container .user-card-info {
        flex: 1;
    }
    
    .quanlyuser-container .user-card-name {
        font-weight: 600;
        color: var(--text);
        margin-bottom: 4px;
    }
    
    .quanlyuser-container .user-card-email {
        color: var(--secondary);
        font-size: 0.9rem;
        margin-bottom: 8px;
    }
    
    .quanlyuser-container .user-card-details {
        margin-bottom: 12px;
    }
    
    .quanlyuser-container .user-card-detail {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }
    
    .quanlyuser-container .user-card-detail-label {
        color: var(--secondary);
        font-size: 0.9rem;
    }
    
    .quanlyuser-container .user-card-detail-value {
        font-weight: 500;
        color: var(--text);
    }
    
    .quanlyuser-container .user-card-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }
}

/* Action Buttons - thao tác */
.quanlyuser-container .action-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
}

.quanlyuser-container .btn-action {
    border-radius: 50%;
    width: 38px; 
    height: 38px;
    display: inline-flex; 
    align-items: center; 
    justify-content: center;
    font-size: 1.1rem;
    transition: box-shadow 0.2s, transform 0.2s;
    border: none;
    cursor: pointer;
}

.quanlyuser-container .btn-edit {
    background: var(--primary);
    color: #fff;
}
.quanlyuser-container .btn-edit:hover { 
    box-shadow: 0 2px 8px #4a90a455; 
    transform: scale(1.08); 
}

.quanlyuser-container .btn-role {
    background: var(--accent);
    color: #fff;
}
.quanlyuser-container .btn-role:hover { 
    box-shadow: 0 2px 8px #f59e0b55; 
    transform: scale(1.08); 
}

.quanlyuser-container .btn-delete {
    background: var(--danger);
    color: #fff;
}
.quanlyuser-container .btn-delete:hover { 
    box-shadow: 0 2px 8px #ef444455; 
    transform: scale(1.08); 
}

@keyframes modalFadeIn { from { opacity: 0; transform: translateY(40px) scale(0.98); } to { opacity: 1; transform: none; } }

/* Body styles when modal is open */
body.modal-open {
    overflow: hidden;
}

/* Ensure modal is above everything */
.modal {
    z-index: 999999 !important;
}

.modal.show {
    z-index: 999999 !important;
}

/* Alert Messages */
.alert-container {
    margin-bottom: 20px;
}

.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert.success {
    background: #f0fdf4;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert.warning {
    background: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.alert.info {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

/* Delete Modal */
.delete-modal .modal-content {
    border-left: 4px solid var(--danger);
}

.delete-modal .modal-header h2 {
    color: var(--danger);
}

.text-danger {
    color: var(--danger) !important;
} 