:root {
    --primary-dark: #232946;
    --primary: #393e6e;
    --primary-light: #5f6caf;
    --accent: #a786df;
    --secondary: #b8c1ec;
    --highlight: #eebbc3;
    --white: #f4f4f6;
    --success: #10b981;
    --danger: #ef4444;
    --gray: #6b7280;
    --border: #393e6e33;
}
body, html { font-family: 'Segoe UI', '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif; }
.contract-wrapper {
    background: var(--primary-dark);
    min-height: 100vh;
    padding: 40px 0 0 0;
}
.breadcrumb {
    background: transparent;
    padding: 0 0 16px 0;
    margin: 0 0 16px 0;
    font-size: 1rem;
    color: var(--secondary);
}
.breadcrumb a { color: var(--accent); text-decoration: underline; }
.breadcrumb .active { color: var(--secondary); }
main {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 80vh;
}
.contract-container {
    width: 100%;
    max-width: 820px;
    background: linear-gradient(135deg, #393e6e 0%, #5f6caf 100%);
    border-radius: 28px;
    box-shadow: 0 8px 32px 0 #23294644;
    overflow: hidden;
    margin-bottom: 40px;
    margin-top: 32px;
    border: 1.5px solid var(--border);
}
header.contract-header {
    background: linear-gradient(90deg, #5f6caf 0%, #a786df 100%);
    color: var(--white);
    padding: 36px 24px 18px 24px;
    text-align: center;
    position: relative;
    border-top-left-radius: 28px;
    border-top-right-radius: 28px;
    box-shadow: 0 2px 12px 0 #23294622;
}
header.contract-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 800;
    letter-spacing: 0.5px;
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 18px;
}
header.contract-header .subtitle {
    margin-top: 10px;
    opacity: 0.95;
    font-size: 1.15rem;
    color: var(--secondary);
    font-weight: 500;
}
.contract-icon {
    width: 3.2rem;
    height: 3.2rem;
    background: var(--primary-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px #23294633;
    border: 2.5px solid var(--accent);
}
.contract-icon svg {
    width: 2.1rem;
    height: 2.1rem;
    color: var(--accent);
}
.contract-body {
    padding: 40px 0 32px 0;
    background: rgba(244,244,246,0.85);
    border-bottom-left-radius: 28px;
    border-bottom-right-radius: 28px;
}
.contract-table {
    width: 90%;
    margin: 0 auto;
    border-radius: 18px;
    background: rgba(255,255,255,0.85);
    box-shadow: 0 2px 12px #23294611;
    overflow: hidden;
    border-collapse: separate;
    border-spacing: 0;
}
.contract-table th, .contract-table td {
    padding: 20px 18px;
    font-size: 1.13rem;
    border-bottom: 1px solid #e3e6f0;
}
.contract-table th {
    background: transparent;
    font-weight: 700;
    color: var(--primary);
    width: 38%;
    vertical-align: top;
    display: flex;
    align-items: center;
    gap: 10px;
    border-right: 1.5px solid #e3e6f0;
}
.contract-table th svg {
    color: var(--accent);
    width: 1.3em;
    height: 1.3em;
}
.contract-table td {
    color: var(--primary-dark);
    font-weight: 600;
    background: transparent;
}
.contract-table tr:last-child th, .contract-table tr:last-child td {
    border-bottom: none;
}
.status-badge {
    padding: 8px 18px;
    border-radius: 20px;
    font-size: 1.05rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-right: 8px;
    background: linear-gradient(90deg, #a786df 0%, #5f6caf 100%);
    color: var(--white);
    box-shadow: 0 2px 8px #23294622;
}
.status-ended {
    background: linear-gradient(90deg, #ef4444 0%, #a786df 100%);
    color: var(--white);
}
.currency-value {
    color: #10b981;
    font-weight: 700;
    font-size: 1.1em;
}
.highlight-value {
    color: var(--primary-dark);
    font-weight: 700;
}
.tenant-list {
    max-width: 320px;
    word-wrap: break-word;
}
.contract-actions {
    text-align: center;
    padding: 28px 32px 32px 32px;
    background: transparent;
    border-top: none;
    display: flex;
    flex-wrap: wrap;
    gap: 18px;
    justify-content: center;
}
.btn-back, .btn-reload {
    background: linear-gradient(90deg, #393e6e 0%, #a786df 100%);
    color: var(--white);
    padding: 13px 32px;
    border: none;
    border-radius: 30px;
    font-size: 1.08rem;
    font-weight: 700;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    box-shadow: 0 4px 12px rgba(167,134,223,0.13);
    cursor: pointer;
    outline: none;
}
.btn-back:hover, .btn-reload:hover {
    transform: translateY(-2px) scale(1.04);
    box-shadow: 0 8px 24px #a786df33;
    color: var(--highlight);
    background: linear-gradient(90deg, #5f6caf 0%, #393e6e 100%);
}
.loading-spinner {
    text-align: center;
    padding: 60px 20px;
    color: var(--primary-light);
}
.loading-spinner svg {
    font-size: 3rem;
    animation: spin 1s linear infinite;
    color: var(--accent);
    margin-bottom: 20px;
    width: 2.5em;
    height: 2.5em;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.error-message {
    text-align: center;
    padding: 60px 20px;
    color: var(--danger);
    background: #fef2f2cc;
    border-radius: 18px;
    border: 1.5px solid #fecaca;
    margin: 32px auto 0 auto;
    max-width: 500px;
    box-shadow: 0 2px 12px #23294622;
}
.error-message svg {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #f87171;
    width: 2.5em;
    height: 2.5em;
}
@media (max-width: 900px) {
    .contract-container { margin: 20px; border-radius: 18px; }
    .contract-body { padding: 18px 0 18px 0; }
    .contract-table th, .contract-table td { padding: 12px; font-size: 1rem; }
    .contract-table th { width: 44%; }
    .contract-actions { padding: 18px; }
}
@media (max-width: 600px) {
    .contract-body { padding: 8px 0 8px 0; }
    .contract-table th, .contract-table td { padding: 8px; font-size: 0.97rem; }
    .contract-header { padding: 12px 4px 8px 4px; }
    .contract-header h1 { font-size: 1.1rem; }
    .contract-container { margin: 8px; }
} 