@import url('contract-management.css');

/* Override màu chủ đạo cho banner-management */
.banner-management, .table-section, .filters-section {
    background: #f8fafc !important;
}
.page-header {
    background: linear-gradient(135deg, #4a90a4 0%, #5aa3b8 100%) !important;
    color: white !important;
}
.banners-table thead {
    background: linear-gradient(135deg, #4a90a4 0%, #5aa3b8 100%) !important;
    color: white !important;
}
.btn-primary, .btn-primary:focus {
    background: #4a90a4 !important;
    border-color: #4a90a4 !important;
    color: white !important;
}
.btn-primary:hover {
    background: #357a8a !important;
    border-color: #357a8a !important;
}
.btn-secondary {
    background: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
}

/* Modal chỉnh lại cho gọn, đẹp */
.modal-content {
    border-radius: 16px !important;
    box-shadow: 0 8px 32px rgba(74, 144, 164, 0.15) !important;
}

/* Form tạo banner nhỏ gọn hơn */
#bannerForm .form-row {
    gap: 16px;
}
#bannerForm .form-group {
    margin-bottom: 12px;
}
#bannerForm textarea[name="Content"] {
    min-height: 40px;
    max-height: 80px;
    resize: vertical;
    font-size: 15px;
    line-height: 1.4;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1.5px solid #e5e7eb;
    background: #fff;
    max-length: 600;
}

/* Giới hạn số từ cho textarea content (hiển thị, không chặn nhập JS) */
#bannerForm textarea[name="Content"]::after {
    content: attr(data-words) ' / 100 từ';
    display: block;
    text-align: right;
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
}

/* Preview ảnh lớn hơn một chút */
#currentImage img, #previewImage {
    max-width: 240px !important;
    max-height: 160px !important;
    border-radius: 10px !important;
}

/* Badge trạng thái */
.status-badge, .badge {
    font-size: 13px !important;
    border-radius: 20px !important;
    padding: 5px 14px !important;
    font-weight: 600 !important;
}
.bg-success, .status-active {
    background: #dcfce7 !important;
    color: #166534 !important;
}
.bg-secondary, .status-completed {
    background: #e5e7eb !important;
    color: #374151 !important;
}

/* Responsive tối ưu cho mobile */
@media (max-width: 768px) {
    .modal-content {
        width: 98vw !important;
        padding: 8px !important;
    }
    #bannerForm .form-row {
        flex-direction: column;
        gap: 0;
    }
}

/* Modern Banner Management Styles */
.banner-management {
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-left h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-left p {
    color: #6c757d;
    margin: 0.5rem 0 0 0;
    font-size: 1.1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* Filters Section */
.filters-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.filters-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-group {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-group select {
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: white;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Table Section */
.table-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.table-responsive {
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.custom-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    margin: 0;
}

.custom-table thead {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.custom-table th,
.custom-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.custom-table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.custom-table tbody tr {
    transition: all 0.3s ease;
}

.custom-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-1px);
}

.btn-group {
    display: flex;
    gap: 0.5rem;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.bg-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.bg-secondary {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
}

/* Mobile Cards */
.mobile-cards {
    display: none;
}

.banner-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.banner-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.items-per-page select {
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    max-width: 500px;
    margin: 40px auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    padding: 32px 24px 24px 24px;
    position: relative;
}

.modal-header {
    border-bottom: 1px solid #eee;
    margin-bottom: 18px;
    text-align: center;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.form-group {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
}

.form-group label {
    flex: 0 0 120px;
    margin-right: 12px;
    font-weight: 500;
}

.form-group input, .form-group textarea, .form-group select {
    flex: 1;
    padding: 8px 10px;
    border-radius: 6px;
    border: 1px solid #ccc;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 18px;
}

#currentImage img {
    border: 1px solid #eee;
    margin-top: 6px;
    border-radius: 6px;
    max-width: 180px;
}

/* Alert */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    position: relative;
    border: none;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .banner-management {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .header-left h1 {
        font-size: 2rem;
    }
    
    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-group {
        justify-content: space-between;
    }
    
    .table-responsive {
        display: none;
    }
    
    .mobile-cards {
        display: block;
    }
    
    .pagination-container {
        flex-direction: column;
        text-align: center;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .modal-footer {
        padding: 1rem 1.5rem;
        flex-direction: column;
    }
}

/* Text utilities */
.text-center { text-align: center; }
.text-muted { color: #6c757d; }
.text-danger { color: #dc3545; }
.text-success { color: #28a745; }
.text-secondary { color: #6c757d; }

.delete-modal .modal-header {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

/* Banner Management - Modern UI Design */
* {
    box-sizing: border-box;
}

.banner-management {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 1rem;
    position: relative;
    z-index: 1;
    margin-left: 0;
    width: 100%;
}

/* Page Header */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-left h1 {
    font-size: 2.2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-left p {
    color: #6c757d;
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

.btn-outline {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
}

/* Filters Section */
.filters-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.filters-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    z-index: 2;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    background: white;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.filter-group select {
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    background: white;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Table Section */
.table-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.banners-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.banners-table thead {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.banners-table th,
.banners-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.banners-table th {
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.banners-table tbody tr {
    transition: background-color 0.2s ease;
}

.banners-table tbody tr:hover {
    background-color: #f8fafc;
}

.btn-group {
    display: flex;
    gap: 0.5rem;
}

.badge {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bg-success {
    background: #dcfce7;
    color: #166534;
}

.bg-secondary {
    background: #f3f4f6;
    color: #374151;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    width: 100%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2);
    animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 24px 24px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
}

.modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    color: white;
    backdrop-filter: blur(10px);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.modal-body {
    padding: 2rem;
    background: white;
}

.modal-footer {
    padding: 2rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    background: #f8fafc;
    border-radius: 0 0 24px 24px;
}

/* Form Styles - Tối ưu cho Banner */
.form-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    flex: 1;
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #1f2937;
    font-size: 0.95rem;
    letter-spacing: 0.25px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.95rem;
    font-family: inherit;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* Content textarea - Tối ưu cho banner content */
#content {
    min-height: 80px !important;
    max-height: 120px !important;
    resize: vertical;
    font-family: inherit;
    line-height: 1.5;
    overflow-y: auto;
}

/* Đảm bảo textarea hoạt động bình thường */
textarea#content {
    border: 2px solid #e5e7eb !important;
    background: white !important;
    color: #1f2937 !important;
    font-size: 0.95rem !important;
    padding: 1rem !important;
    border-radius: 12px !important;
    transition: all 0.3s ease !important;
}

textarea#content:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15) !important;
    outline: none !important;
}

/* Image upload và preview */
.form-group input[type="file"] {
    padding: 1rem;
    border: 2px dashed #d1d5db;
    background: linear-gradient(145deg, #f9fafb 0%, #f3f4f6 100%);
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 500;
}

.form-group input[type="file"]:hover {
    border-color: #667eea;
    background: linear-gradient(145deg, #f0f4ff 0%, #e0e7ff 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

/* Image preview container - Không che form */
#currentImage {
    margin-top: 1rem;
    text-align: center;
    max-width: 100%;
    padding: 1rem;
    background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

#currentImage img,
#previewImage {
    max-width: 240px !important;
    max-height: 140px !important;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.5);
    object-fit: cover;
    border: 3px solid white;
    transition: transform 0.3s ease;
}

#currentImage img:hover,
#previewImage:hover {
    transform: scale(1.05);
}

/* Error messages */
.error-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: block;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
}

.required {
    color: #dc3545;
    font-weight: 600;
}

/* Alert messages */
.alert {
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-error {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.alert-close {
    background: none;
    border: none;
    margin-left: auto;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-info {
    color: #6b7280;
    font-size: 0.9rem;
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.items-per-page select {
    padding: 0.5rem;
    border-radius: 6px;
    border: 1px solid #d1d5db;
}

.pagination-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-number:hover,
.page-number.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Mobile Cards */
.mobile-cards {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banner-management {
        padding: 0.5rem;
    }
    
    .page-header {
        padding: 1.5rem;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .header-left h1 {
        font-size: 1.8rem;
    }
    
    .filters-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .table-responsive {
        display: none;
    }
    
    .mobile-cards {
        display: block;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .modal-content {
        margin: 0;
        border-radius: 15px;
        max-height: 95vh;
    }
    
    .modal-footer {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
    
    .pagination-container {
        justify-content: center;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .banner-management {
        padding: 0.25rem;
    }
    
    .page-header,
    .filters-section,
    .table-section {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

/* Utility classes */
.text-center { text-align: center; }
.text-muted { color: #6c757d; }
.text-danger { color: #dc3545; }
.text-success { color: #28a745; }
.text-secondary { color: #6c757d; }

/* Banner card cho mobile */
.banner-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.banner-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}