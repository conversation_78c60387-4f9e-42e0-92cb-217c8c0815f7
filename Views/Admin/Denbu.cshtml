@{
    ViewData["Title"] = "Quản lý đền bù";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý đền bù</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">Dashboard</a></li>
        <li class="breadcrumb-item active">Đ<PERSON>n bù</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i>
                Danh sách đền bù
            </div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDenbuModal">
                <i class="fas fa-plus"></i> Thêm đền bù mới
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="denbuTable">
                    <thead class="table-light">
                        <tr>
                            <th>Mã đền bù</th>
                            <th>Mã hợp đồng</th>
                            <th>Nội dung</th>
                            <th>Số tiền</th>
                            <th>Hình ảnh</th>
                            <th>Ngày tạo</th>
                            <th width="150">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Denbu Modal -->
<div class="modal fade" id="addDenbuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Thêm đền bù mới</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDenbuForm">
                    <div class="mb-3">
                        <label class="form-label">Hợp đồng</label>
                        <select class="form-select" id="addMaHopDong" required>
                            <option value="">Chọn hợp đồng</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nội dung</label>
                        <textarea class="form-control" id="addNoiDung" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số tiền</label>
                        <input type="number" class="form-control" id="addSoTien" required min="0" step="1000">
                    </div>
                    <div class="form-group">
                        <label for="imageFile">Hình ảnh</label>
                        <input type="file" id="imageFile" name="imageFile" accept="image/*">
                        <div class="file-info">
                            <small>Chấp nhận: JPG, PNG, GIF. Tối đa 5MB</small>
                        </div>
                        <div class="error-message" id="imageFileError"></div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="image-preview" style="display: none;">
                            <img id="previewImg" src="" alt="Preview">
                            <button type="button" id="removeImageBtn" class="remove-image-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="saveDenbuBtn">Lưu</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Denbu Modal -->
<div class="modal fade" id="editDenbuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Sửa thông tin đền bù</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editDenbuForm">
                    <input type="hidden" id="editMaDenBu">
                    <div class="mb-3">
                        <label class="form-label">Hợp đồng</label>
                        <select class="form-select" id="editMaHopDong" required>
                            <option value="">Chọn hợp đồng</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nội dung</label>
                        <textarea class="form-control" id="editNoiDung" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số tiền</label>
                        <input type="number" class="form-control" id="editSoTien" required min="0" step="1000">
                    </div>
                    <div class="form-group">
                        <label for="imageFile">Hình ảnh</label>
                        <input type="file" id="imageFile" name="imageFile" accept="image/*">
                        <div class="file-info">
                            <small>Chấp nhận: JPG, PNG, GIF. Tối đa 5MB</small>
                        </div>
                        <div class="error-message" id="imageFileError"></div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="image-preview" style="display: none;">
                            <img id="previewImg" src="" alt="Preview">
                            <button type="button" id="removeImageBtn" class="remove-image-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="updateDenbuBtn">Cập nhật</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <style>
        .modal-content {
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }
        .modal-header {
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
        }
        .modal-footer {
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .form-label {
            font-weight: 500;
        }
        .form-control, .form-select {
            border-radius: 8px;
            padding: 10px 14px;
            font-size: 1rem;
        }
        .btn-primary, .btn-secondary, .btn-info, .btn-danger {
            border-radius: 8px;
        }
        .swal2-title {
            font-size: 1.5rem;
        }
        .swal2-html-container {
            text-align: left;
            font-size: 1rem;
        }
    </style>
    <script>
        $(document).ready(function () {
            // Load danh sách đền bù
            function loadDenbus() {
                $.get('/api/Denbu/get-denbu', function (response) {
                    if (response.success) {
                        const tbody = $('#denbuTable tbody');
                        tbody.empty();
                        response.data.forEach(denbu => {
                            const imageHtml = roomType.imageBase64
                                ? `<img src="data:image/jpeg;base64,${denbu.base64}" class="room-image" alt="${denbu.maDenBu}">`
                                : `<div class="no-image">Không có ảnh</div>`;

                            tbody.append(`
                                <tr>
                                    <td>${denbu.maDenBu}</td>
                                    <td>#${denbu.maHopDong}</td>
                                    <td>${denbu.noiDung}</td>
                                    <td>${denbu.soTien.toLocaleString('vi-VN')} VNĐ</td>
                                            <td>${imageHtml}</td>
                                    <td>${new Date(denbu.ngayTao).toLocaleDateString('vi-VN')}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                             <button type="button" class="btn btn-info btn-sm" onclick="viewDenbu(${denbu.maDenBu})" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="editDenbu(${denbu.maDenBu})" title="Sửa">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteDenbu(${denbu.maDenBu})" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        });
                    }
                });
            }

            // Load danh sách hợp đồng cho dropdown
            function loadContractsDropdown() {
                $.get('/api/Contract/get-contracts', function (response) {
                    if (response.success) {
                        var options = '<option value="">Chọn hợp đồng</option>';
                        response.data.forEach(function (c) {
                            var room = c.room ? c.room.tenPhong : 'Chưa có phòng';
                            options += `<option value="${c.contractId}">#${c.contractId} - ${room}</option>`;
                        });
                        $('#addMaHopDong').html(options);
                        $('#editMaHopDong').html(options);
                    }
                });
            }

            // Khởi tạo
            loadDenbus();
            loadContractsDropdown();

            // Reload dropdown khi mở modal để đảm bảo dữ liệu mới nhất
            $('#addDenbuModal, #editDenbuModal').on('show.bs.modal', loadContractsDropdown);

            // Thêm đền bù mới
            $('#saveDenbuBtn').click(function () {
                var formData = new FormData();
                formData.append('maHopDong', $('#addMaHopDong').val());
                formData.append('noiDung', $('#addNoiDung').val());
                formData.append('soTien', $('#addSoTien').val());
                var file = $('#addDenbuForm #imageFile')[0].files[0];
                if (file) formData.append('hinhanh', file);

                if (!$('#addMaHopDong').val() || !$('#addNoiDung').val() || !$('#addSoTien').val()) {
                    toastr.error('Vui lòng nhập đầy đủ thông tin');
                    return;
                }

                $.ajax({
                    url: '/api/Denbu/add-denbu',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.success) {
                            $('#addDenbuModal').modal('hide');
                            $('#addDenbuForm')[0].reset();
                            loadDenbus();
                            toastr.success('Thêm đền bù thành công');
                        } else {
                            toastr.error(response.message || 'Có lỗi xảy ra khi thêm mới.');
                        }
                    },
                    error: function () {
                        toastr.error('Đã có lỗi xảy ra. Vui lòng thử lại.');
                    }
                });
            });
            
            // Xem chi tiết đền bù
            window.viewDenbu = function (id) {
                // Endpoint này từ chức năng sửa, có thể cần endpoint chi tiết hơn
                $.get(`/api/Denbu/get-denbu-mahopdong/${id}`, function (response) {
                    if (response.success && response.data) {
                        const denbu = response.data;
                        Swal.fire({
                            title: '<strong>Chi tiết đền bù</strong>',
                            icon: 'info',
                            html: `
                                <div class="text-start p-2">
                                    <p><strong>Mã đền bù:</strong> ${denbu.maDenBu}</p>
                                    <p><strong>Mã hợp đồng:</strong> #${denbu.maHopDong}</p>
                                    <p><strong>Ngày tạo:</strong> ${new Date(denbu.ngayTao).toLocaleDateString('vi-VN')}</p>
                                    <p><strong>Số tiền:</strong> ${denbu.soTien.toLocaleString('vi-VN')} VNĐ</p>
                                    <hr>
                                    <p><strong>Nội dung:</strong></p>
                                    <p>${denbu.noiDung}</p>
                                </div>
                            `,
                            showCloseButton: true,
                            focusConfirm: false,
                            confirmButtonText: '<i class="fa fa-thumbs-up"></i> OK!',
                            confirmButtonAriaLabel: 'Thumbs up, great!',
                        });
                    } else {
                        toastr.error('Không thể tải chi tiết đền bù.');
                    }
                });
            };

            // Mở modal sửa đền bù
            window.editDenbu = function (id) {
                $.get(`/api/Denbu/get-denbu-mahopdong/${id}`, function (response) {
                    if (response.success && response.data) {
                        $('#editMaDenBu').val(response.data.maDenBu);
                        $('#editMaHopDong').val(response.data.maHopDong);
                        $('#editNoiDung').val(response.data.noiDung);
                        $('#editSoTien').val(response.data.soTien);
                        $('#editDenbuModal').modal('show');
                    } else {
                        toastr.error('Không tìm thấy thông tin đền bù.');
                    }
                });
            }

            // Cập nhật đền bù
            $('#updateDenbuBtn').click(function () {
                var id = $('#editMaDenBu').val();
                var formData = new FormData();
                formData.append('maHopDong', $('#editMaHopDong').val());
                formData.append('noiDung', $('#editNoiDung').val());
                formData.append('soTien', $('#editSoTien').val());
                var file = $('#editDenbuForm #imageFile')[0].files[0];
                if (file) formData.append('hinhanh', file);

                if (!$('#editMaHopDong').val() || !$('#editNoiDung').val() || !$('#editSoTien').val()) {
                    toastr.error('Vui lòng nhập đầy đủ thông tin');
                    return;
                }
                
                $.ajax({
                    url: `/api/Denbu/edit-denbu/${id}`,
                    type: 'PUT',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.success) {
                            $('#editDenbuModal').modal('hide');
                            loadDenbus();
                            toastr.success('Cập nhật đền bù thành công');
                        } else {
                            toastr.error(response.message || 'Có lỗi xảy ra khi cập nhật.');
                        }
                    },
                    error: function () {
                        toastr.error('Đã có lỗi xảy ra. Vui lòng thử lại.');
                    }
                });
            });

            // Xóa đền bù
            window.deleteDenbu = function (id) {
                Swal.fire({
                    title: 'Bạn có chắc không?',
                    text: "Bạn sẽ không thể hoàn tác hành động này!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Vâng, xóa nó!',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `/api/Denbu/delete-denbu/${id}`,
                            type: 'DELETE',
                            success: function (response) {
                                if (response.success) {
                                    loadDenbus();
                                    Swal.fire(
                                        'Đã xóa!',
                                        'Bản ghi đền bù đã được xóa thành công.',
                                        'success'
                                    )
                                } else {
                                    toastr.error(response.message || 'Có lỗi xảy ra khi xóa.');
                                }
                            },
                            error: function () {
                                toastr.error('Đã có lỗi xảy ra. Vui lòng thử lại.');
                            }
                        });
                    }
                })
            }
        });
    </script>
} 