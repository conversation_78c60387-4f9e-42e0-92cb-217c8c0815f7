@{
    ViewData["Title"] = "Quản lý hóa đơn";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Quản lý hóa đơn</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">Dashboard</a></li>
        <li class="breadcrumb-item active">Hóa đơn</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i>
                Danh sách hóa đơn
            </div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBillModal">
                <i class="fas fa-plus"></i> Thêm hóa đơn mới
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="utilityBillTable">
                    <thead class="table-light">
                        <tr>
                            <th>Mã hóa đơn</th>
                            <th>Phòng</th>
                            <th>Tháng/Năm</th>
                            <th>Số điện</th>
                            <th>Số nước</th>
                            <th>Phí DV</th>
                            <th>Số xe</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th width="150">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Bill Modal -->
<div class="modal fade" id="addBillModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Thêm hóa đơn mới</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addBillForm">
                    <div class="mb-3">
                        <label class="form-label">Phòng</label>
                        <select class="form-select" id="maPhong" required>
                            <option value="">Chọn phòng</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số điện (kWh)</label>
                        <input type="number" class="form-control" id="soDien" required min="0" step="0.1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số nước (m³)</label>
                        <input type="number" class="form-control" id="soNuoc" required min="0" step="0.1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ghi chú</label>
                        <textarea class="form-control" id="note" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="saveBillBtn">Lưu</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Bill Modal -->
<div class="modal fade" id="editBillModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Sửa hóa đơn</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editBillForm">
                    <input type="hidden" id="editMaHoaDon">
                    <div class="mb-3">
                        <label class="form-label">Phòng</label>
                        <input type="text" class="form-control" id="editTenPhong" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số điện (kWh)</label>
                        <input type="number" class="form-control" id="editSoDien" required min="0" step="0.1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số nước (m³)</label>
                        <input type="number" class="form-control" id="editSoNuoc" required min="0" step="0.1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ghi chú</label>
                        <textarea class="form-control" id="editNote" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="editDaThanhToan">
                            <label class="form-check-label">Đã thanh toán</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="updateBillBtn">Cập nhật</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Load danh sách phòng
            function loadPhongList() {
                $.get('/api/UtilityBill/get-all-phong', function (response) {
                    if (response.success) {
                        const phongSelect = $('#maPhong');
                        phongSelect.empty().append('<option value="">Chọn phòng</option>');
                        response.data.forEach(phong => {
                            phongSelect.append(`<option value="${phong.maPhong}">${phong.tenPhong}</option>`);
                        });
                    }
                });
            }

            // Load danh sách hóa đơn
            function loadUtilityBills() {
                $.get('/api/UtilityBill/get-all-hoa-don', function (response) {
                    if (response.success) {
                        const tbody = $('#utilityBillTable tbody');
                        tbody.empty();
                        response.data.forEach(bill => {
                            tbody.append(`
                                <tr>
                                    <td>${bill.maHoaDon}</td>
                                    <td>${bill.tenPhong}</td>
                                    <td>${bill.thang}/${bill.nam}</td>
                                    <td>${bill.soDien}</td>
                                    <td>${bill.soNuoc}</td>
                                    <td>${bill.phidv?.toLocaleString('vi-VN')} VNĐ</td>
                                    <td>${bill.soxe}</td>
                                    <td>${bill.tongTien?.toLocaleString('vi-VN')} VNĐ</td>
                                    <td>
                                        <span class="badge ${bill.daThanhToan ? 'bg-success' : 'bg-warning'}">
                                            ${bill.daThanhToan ? 'Đã thanh toán' : 'Chưa thanh toán'}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-info btn-sm" onclick="viewBill(${bill.maHoaDon})" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="editBill(${bill.maHoaDon})" title="Sửa">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteBill(${bill.maHoaDon})" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        });
                    }
                });
            }

            // Thêm hóa đơn mới
            $('#saveBillBtn').click(function () {
                const data = {
                    maPhong: $('#maPhong').val(),
                    soDien: $('#soDien').val(),
                    soNuoc: $('#soNuoc').val(),
                    note: $('#note').val()
                };

                if (!data.maPhong) {
                    toastr.error('Vui lòng chọn phòng');
                    return;
                }

                $.ajax({
                    url: '/api/UtilityBill/add-hoadon-tien-ich',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function (response) {
                        if (response.success) {
                            $('#addBillModal').modal('hide');
                            $('#addBillForm')[0].reset();
                            loadUtilityBills();
                            toastr.success('Thêm hóa đơn thành công');
                        } else {
                            toastr.error(response.message || 'Có lỗi xảy ra');
                        }
                    },
                    error: function () {
                        toastr.error('Có lỗi xảy ra khi thêm hóa đơn');
                    }
                });
            });

            // Xem chi tiết hóa đơn
            window.viewBill = function (id) {
                $.get(`/api/UtilityBill/print-hoa-don/${id}`, function (response) {
                    if (response.success) {
                        const bill = response.data;
                        Swal.fire({
                            title: 'Chi tiết hóa đơn',
                            html: `
                                <div class="text-start">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <p><strong>Mã hóa đơn:</strong> ${bill.maHoaDon}</p>
                                            <p><strong>Phòng:</strong> ${bill.tenPhong}</p>
                                            <p><strong>Tháng/Năm:</strong> ${bill.thangNam}</p>
                                            <p><strong>Ngày xuất:</strong> ${bill.ngayXuat}</p>
                                        </div>
                                        <div class="col-6">
                                            <p><strong>Khách hàng:</strong> ${bill.khachHang}</p>
                                            <p><strong>Trạng thái:</strong> ${bill.daThanhToan}</p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-12">
                                            <h6 class="mb-3">Chi tiết thanh toán</h6>
                                            <table class="table table-sm">
                                                <tr>
                                                    <td>Số điện:</td>
                                                    <td>${bill.soDien} kWh</td>
                                                    <td>Đơn giá:</td>
                                                    <td>${bill.donGiaDien?.toLocaleString('vi-VN')} VNĐ/kWh</td>
                                                    <td>Thành tiền:</td>
                                                    <td>${bill.thanhTienDien?.toLocaleString('vi-VN')} VNĐ</td>
                                                </tr>
                                                <tr>
                                                    <td>Số nước:</td>
                                                    <td>${bill.soNuoc} m³</td>
                                                    <td>Đơn giá:</td>
                                                    <td>${bill.donGiaNuoc?.toLocaleString('vi-VN')} VNĐ/m³</td>
                                                    <td>Thành tiền:</td>
                                                    <td>${bill.thanhTienNuoc?.toLocaleString('vi-VN')} VNĐ</td>
                                                </tr>
                                                <tr>
                                                    <td>Tiền phòng:</td>
                                                    <td colspan="5">${bill.tienPhong?.toLocaleString('vi-VN')} VNĐ</td>
                                                </tr>
                                                <tr>
                                                    <td>Phí dịch vụ:</td>
                                                    <td colspan="5">${bill.phidv?.toLocaleString('vi-VN')} VNĐ</td>
                                                </tr>
                                                <tr>
                                                    <td>Số xe:</td>
                                                    <td colspan="5">${bill.soxe}</td>
                                                </tr>
                                                <tr class="table-primary">
                                                    <td colspan="4"><strong>Tổng tiền:</strong></td>
                                                    <td colspan="2"><strong>${bill.tongTien?.toLocaleString('vi-VN')} VNĐ</strong></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            `,
                            width: '800px',
                            confirmButtonText: 'Đóng',
                            confirmButtonColor: '#3085d6'
                        });
                    }
                });
            };

            // Sửa hóa đơn
            window.editBill = function (id) {
                $.get(`/api/UtilityBill/get-hoa-don-chi-tiet/${id}`, function (response) {
                    if (response.success) {
                        const bill = response.data;
                        $('#editMaHoaDon').val(bill.maHoaDon);
                        $('#editTenPhong').val(bill.tenPhong);
                        $('#editSoDien').val(bill.soDien);
                        $('#editSoNuoc').val(bill.soNuoc);
                        $('#editDaThanhToan').prop('checked', bill.daThanhToan);
                        $('#editBillModal').modal('show');
                    }
                });
            };

            // Cập nhật hóa đơn
            $('#updateBillBtn').click(function () {
                const id = $('#editMaHoaDon').val();
                const data = {
                    maPhong: $('#editMaPhong').val(),
                    soDien: $('#editSoDien').val(),
                    soNuoc: $('#editSoNuoc').val(),
                    note: $('#editNote').val()
                };

                $.ajax({
                    url: `/api/UtilityBill/edit-hoadon-tien-ich/${id}?DaThanhToan=${$('#editDaThanhToan').val()}`,
                    type: 'PUT',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function (response) {
                        if (response.success) {
                            $('#editBillModal').modal('hide');
                            loadUtilityBills();
                            toastr.success('Cập nhật hóa đơn thành công');
                        } else {
                            toastr.error(response.message || 'Có lỗi xảy ra');
                        }
                    },
                    error: function () {
                        toastr.error('Có lỗi xảy ra khi cập nhật hóa đơn');
                    }
                });
            });

            // Xóa hóa đơn
            window.deleteBill = function (id) {
                Swal.fire({
                    title: 'Xác nhận xóa?',
                    text: "Bạn có chắc chắn muốn xóa hóa đơn này?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Xóa',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `/api/UtilityBill/delete-hoadon-tienich/${id}`,
                            type: 'DELETE',
                            success: function (response) {
                                if (response.success) {
                                    loadUtilityBills();
                                    toastr.success('Xóa hóa đơn thành công');
                                } else {
                                    toastr.error(response.message || 'Có lỗi xảy ra');
                                }
                            },
                            error: function () {
                                toastr.error('Có lỗi xảy ra khi xóa hóa đơn');
                            }
                        });
                    }
                });
            };

            // Initial load
            loadPhongList();
            loadUtilityBills();

            // Configure toastr
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "timeOut": "3000"
            };
        });
    </script>
} 