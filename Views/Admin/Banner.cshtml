@{
    ViewData["Title"] = "Quản lý Banners";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="banner-management">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1><i class="fas fa-images"></i> Quản lý Banners</h1>
                <p>Thêm, sửa, xóa và quản lý các banners của hệ thống</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" id="addBannerBtn">
                    <i class="fas fa-plus"></i> Thêm Banner
                </button>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <div class="filters-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Tì<PERSON> kiếm theo tên banner...">
            </div>
            <div class="filter-group">
                <select id="statusFilter" class="form-select">
                    <option value="">Tất cả trạng thái</option>
                    <option value="true">Hoạt động</option>
                    <option value="false">Không hoạt động</option>
                </select>
            </div>
            <div class="filter-actions">
                <button class="btn btn-secondary" id="clearFiltersBtn">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer"></div>

    <!-- Data Table -->
    <div class="table-section">
        <div class="table-container">
            <div class="table-responsive">
                <table class="data-table" id="bannersTable">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Hình ảnh</th>
                            <th>Tên Banner</th>
                            <th>Nội dung</th>
                            <th>Trạng thái</th>
                            <th>URL</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="bannersTableBody">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Cards -->
        <div class="mobile-cards" id="mobileBannerCards">
            <!-- Mobile cards will be loaded here -->
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Hiển thị 0 - 0 của 0 banners</span>
                <div class="items-per-page">
                    <label for="itemsPerPage">Hiển thị:</label>
                    <select id="itemsPerPage">
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                    <span>banners/trang</span>
                </div>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-outline" id="prevPageBtn" disabled>
                    <i class="fas fa-chevron-left"></i> Trước
                </button>
                <div class="page-numbers" id="pageNumbers"></div>
                <button class="btn btn-outline" id="nextPageBtn" disabled>
                    Sau <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Banner Modal -->
<div class="modal" id="bannerModal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">
                <i class="fas fa-images"></i>
                <span id="modalTitleText">Thêm Banner</span>
            </h2>
            <button class="modal-close" id="closeModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="bannerForm" class="modal-body">
            <div class="form-group">
                <label for="title">Tên Banner <span class="required">*</span></label>
                <input type="text" id="title" name="title" required maxlength="255"
                       placeholder="Nhập tên banner">
                <div class="error-message" id="titleError"></div>
            </div>

            <div class="form-group">
                <label for="bannerContent">Nội dung Banner</label>
                <textarea id="bannerContent" name="content" rows="4" maxlength="200"
                          placeholder="Nhập nội dung banner (tối đa 200 ký tự)"></textarea>
                <div class="char-counter">
                    <span id="contentCounter">0/200</span>
                </div>
                <div class="error-message" id="contentError"></div>
            </div>

            <div class="form-group">
                <label for="text">Văn bản nút</label>
                <input type="text" id="text" name="text" maxlength="50"
                       placeholder="Ví dụ: Xem chi tiết, Mua ngay...">
                <div class="error-message" id="textError"></div>
            </div>

            <div class="form-group">
                <label for="redirectUrl">URL chuyển hướng</label>
                <input type="url" id="redirectUrl" name="redirectUrl"
                       placeholder="https://example.com">
                <div class="error-message" id="redirectUrlError"></div>
            </div>

            <div class="form-group">
                <label for="isActive">Trạng thái <span class="required">*</span></label>
                <select id="isActive" name="isActive" required>
                    <option value="">Chọn trạng thái</option>
                    <option value="true">Hoạt động</option>
                    <option value="false">Không hoạt động</option>
                </select>
                <div class="error-message" id="isActiveError"></div>
            </div>

            <div class="form-group">
                <label for="imageFile">Hình ảnh Banner</label>
                <div class="file-upload">
                    <input type="file" id="imageFile" name="imageFile" accept="image/*">
                    <div class="file-upload-display">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Chọn hình ảnh hoặc kéo thả vào đây</span>
                        <small>Chấp nhận: JPG, PNG, GIF. Tối đa 5MB</small>
                    </div>
                </div>
                <div class="error-message" id="imageFileError"></div>

                <!-- Image Preview -->
                <div id="imagePreview" class="image-preview" style="display: none;">
                    <img id="previewImg" src="" alt="Preview">
                    <div class="image-preview-overlay">
                        <button type="button" id="removeImageBtn" class="remove-image-btn">
                            <i class="fas fa-trash"></i> Xóa ảnh
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-primary" id="saveBannerBtn">
                <i class="fas fa-save"></i> <span id="saveBtnText">Lưu</span>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteModal">
    <div class="modal-backdrop"></div>
    <div class="modal-content delete-modal">
        <div class="modal-header">
            <h2><i class="fas fa-exclamation-triangle" style="color: var(--danger);"></i> Xác nhận xóa</h2>
            <button class="modal-close" id="closeDeleteModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
            <p>Bạn có chắc chắn muốn xóa banner <strong id="deleteBannerName"></strong> không?</p>
            <p style="color: var(--danger); margin-top: 15px;">
                <i class="fas fa-exclamation-triangle"></i> 
                Hành động này không thể hoàn tác!
            </p>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i> Xóa
            </button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Đang xử lý...</p>
    </div>
</div>


<link href="~/css/banner_admin.css" rel="stylesheet" />
<script src="~/js/banner-management.js"></script>