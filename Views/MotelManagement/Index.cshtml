@{
    ViewData["Title"] = "Quản lý Nhà Trọ";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="motel-management">
    <div class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1><i class="fas fa-building"></i> Quản lý Nhà Trọ</h1>
                <p>Thêm, sửa, xóa và quản lý thông tin nhà trọ</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" id="addMotelBtn">
                    <i class="fas fa-plus"></i> Thêm nhà trọ
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <div class="filters-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Tìm kiếm theo tên nhà trọ hoặc địa chỉ...">
            </div>
            
            <div class="filter-group">
                <select id="provinceFilter">
                    <option value="">Tất cả tỉnh/thành</option>
                </select>
                
                <select id="areaFilter">
                    <option value="">Tất cả khu vực</option>
                </select>
                
                <button class="btn btn-secondary" id="clearFiltersBtn">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Motels Table -->
    <div class="table-section">
        <div class="table-container">
            <div class="table-responsive">
                <table class="motels-table">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Tên nhà trọ</th>
                            <th>Địa chỉ</th>
                            <th>Tỉnh/Thành</th>
                            <th>Khu vực</th>
                            <th>Chủ trọ</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="motelsTableBody">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Cards -->
        <div class="mobile-cards" id="mobileMotelCards">
            <!-- Mobile cards will be loaded here -->
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Hiển thị 0 - 0 của 0 nhà trọ</span>
                <div class="items-per-page">
                    <label for="itemsPerPage">Hiển thị:</label>
                    <select id="itemsPerPage">
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                    <span>nhà trọ/trang</span>
                </div>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-outline" id="prevPageBtn" disabled>
                    <i class="fas fa-chevron-left"></i> Trước
                </button>
                <div class="page-numbers" id="pageNumbers"></div>
                <button class="btn btn-outline" id="nextPageBtn" disabled>
                    Sau <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Motel Modal -->
<div class="modal" id="motelModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">
                <i class="fas fa-building"></i> 
                <span id="modalTitleText">Thêm nhà trọ</span>
            </h2>
            <button class="modal-close" id="closeModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="motelForm" class="modal-body">
            <div class="form-row">
                <div class="form-group">
                    <label for="tenNhaTro">Tên nhà trọ <span class="required">*</span></label>
                    <input type="text" id="tenNhaTro" name="tenNhaTro" required 
                           placeholder="Nhập tên nhà trọ">
                    <div class="error-message" id="tenNhaTroError"></div>
                </div>
                
                <div class="form-group">
                    <label for="diaChi">Địa chỉ <span class="required">*</span></label>
                    <input type="text" id="diaChi" name="diaChi" required 
                           placeholder="Nhập địa chỉ chi tiết">
                    <div class="error-message" id="diaChiError"></div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="maTinh">Tỉnh/Thành <span class="required">*</span></label>
                    <select id="maTinh" name="maTinh" required>
                        <option value="">Chọn tỉnh/thành</option>
                    </select>
                    <div class="error-message" id="maTinhError"></div>
                </div>
                
                <div class="form-group">
                    <label for="maKhuVuc">Khu vực <span class="required">*</span></label>
                    <select id="maKhuVuc" name="maKhuVuc" required>
                        <option value="">Chọn khu vực</option>
                    </select>
                    <div class="error-message" id="maKhuVucError"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="maChuTro">Chủ trọ <span class="required">*</span></label>
                <select id="maChuTro" name="maChuTro" required>
                    <option value="">Chọn chủ trọ</option>
                </select>
                <div class="error-message" id="maChuTroError"></div>
            </div>
            
            <div class="form-group">
                <label for="moTa">Mô tả</label>
                <textarea id="moTa" name="moTa" rows="4" 
                         placeholder="Nhập mô tả về nhà trọ (tùy chọn)"></textarea>
            </div>
            <div class="form-group">
                <label for="gg_map">Link Google Map</label>
                <input type="text" id="gg_map" name="gg_map" placeholder="Dán link Google Map hoặc iframe...">
                <div class="error-message" id="gg_mapError"></div>
            </div>
        </form>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-primary" id="saveMotelBtn">
                <i class="fas fa-save"></i> <span id="saveBtnText">Lưu</span>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteModal">
    <div class="modal-content delete-modal">
        <div class="modal-header">
            <h2><i class="fas fa-exclamation-triangle text-danger"></i> Xác nhận xóa</h2>
            <button class="modal-close" id="closeDeleteModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <p>Bạn có chắc chắn muốn xóa nhà trọ <strong id="deleteMotelName"></strong> không?</p>
            <p class="text-danger"><i class="fas fa-warning"></i> Hành động này không thể hoàn tác!</p>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i> Xóa
            </button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Đang tải dữ liệu...</p>
    </div>
</div>

<style>
    /* Ensure modal is properly displayed */
    .modal {
        z-index: 99999 !important;
    }
    
    .modal-content {
        max-height: 90vh !important;
        overflow: visible !important;
    }
    
    .modal-body {
        max-height: calc(90vh - 140px) !important;
        overflow-y: auto !important;
    }
    
    .modal-footer {
        position: sticky !important;
        bottom: 0 !important;
        background: #f8fafc !important;
        border-top: 1px solid #e5e7eb !important;
    }
</style>

<style>
    /* Enhanced modal styling for better PC display */
    @@media (min-width: 768px) {
        .modal {
            z-index: 99999 !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 30px !important;
        }
        
        .modal-content {
            width: 550px !important;
            max-width: 90vw !important;
            max-height: 85vh !important;
            overflow: visible !important;
            margin: 0 auto !important;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3) !important;
        }
        
        .modal-body {
            max-height: calc(85vh - 160px) !important;
            overflow-y: auto !important;
            padding: 25px 30px !important;
        }
        
        .modal-header,
        .modal-footer {
            padding: 20px 30px !important;
            flex-shrink: 0 !important;
        }
        
        .modal-footer {
            position: sticky !important;
            bottom: 0 !important;
            background: #f8fafc !important;
            border-top: 1px solid #e5e7eb !important;
        }
    }
    
    @@media (max-width: 767px) {
        .modal {
            z-index: 99999 !important;
            padding: 10px !important;
        }
        
        .modal-content {
            max-height: 95vh !important;
            width: 100% !important;
        }
        
        .modal-body {
            max-height: calc(95vh - 140px) !important;
            overflow-y: auto !important;
        }
    }
</style>

<style>
    /* Pagination styles */
    .pagination-info {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }
    
    .items-per-page {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
    }
    
    .items-per-page label {
        margin: 0;
        font-weight: 500;
        color: #6b7280;
    }
    
    .items-per-page select {
        padding: 4px 8px;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        background: white;
        font-size: 14px;
        min-width: 60px;
    }
    
    .items-per-page select:focus {
        outline: none;
        border-color: #4a90a4;
        box-shadow: 0 0 0 2px rgba(74, 144, 164, 0.1);
    }
    
    .items-per-page span {
        color: #6b7280;
    }
    
    @@media (max-width: 768px) {
        .pagination-info {
            justify-content: center;
            gap: 15px;
        }
        
        .items-per-page {
            font-size: 13px;
        }
    }
</style>

<link href="~/css/motel-management.css?@DateTime.Now" rel="stylesheet">
<script src="~/js/motel-management.js?@DateTime.Now"></script> 