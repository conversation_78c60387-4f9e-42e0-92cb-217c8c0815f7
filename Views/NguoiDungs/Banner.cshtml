@{
    ViewData["Title"] = "Quản lý Loại Phòng";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="roomtype-management">
    <div class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1><i class="fas fa-home"></i> Quản lý Loại Phòng</h1>
                <p>Thêm, sửa, xóa và quản lý các loại phòng trọ</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" id="addRoomTypeBtn">
                    <i class="fas fa-plus"></i> Thêm loại phòng
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <div class="filters-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Tìm kiếm theo tên loại phòng...">
            </div>

            <div class="filter-group">
                <button class="btn btn-secondary" id="clearFiltersBtn">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Room Types Table -->
    <div class="table-section">
        <div class="table-container">
            <div class="table-responsive">
                <table class="roomtypes-table">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Hình ảnh</th>
                            <th>Tên loại phòng</th>
                            <th>Mô tả</th>
                            <th>Số phòng</th>
                            <th>URL chuyển hướng</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="roomTypesTableBody">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Cards -->
        <div class="mobile-cards" id="mobileRoomTypeCards">
            <!-- Mobile cards will be loaded here -->
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Hiển thị 0 - 0 của 0 loại phòng</span>
                <div class="items-per-page">
                    <label for="itemsPerPage">Hiển thị:</label>
                    <select id="itemsPerPage">
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                    <span>loại phòng/trang</span>
                </div>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-outline" id="prevPageBtn" disabled>
                    <i class="fas fa-chevron-left"></i> Trước
                </button>
                <div class="page-numbers" id="pageNumbers"></div>
                <button class="btn btn-outline" id="nextPageBtn" disabled>
                    Sau <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Room Type Modal -->
<div class="modal" id="roomTypeModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">
                <i class="fas fa-home"></i>
                <span id="modalTitleText">Thêm loại phòng</span>
            </h2>
            <button class="modal-close" id="closeModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="roomTypeForm" class="modal-body" enctype="multipart/form-data">
            <div class="form-group">
                <label for="tenTheLoai">Tên loại phòng <span class="required">*</span></label>
                <input type="text" id="tenTheLoai" name="tenTheLoai" required
                       placeholder="Nhập tên loại phòng">
                <div class="error-message" id="tenTheLoaiError"></div>
            </div>

            <div class="form-group">
                <label for="moTa">Mô tả</label>
                <textarea id="moTa" name="moTa" rows="4"
                          placeholder="Nhập mô tả về loại phòng (tùy chọn)"></textarea>
                <div class="error-message" id="moTaError"></div>
            </div>

            <div class="form-group">
                <label for="imageFile">Hình ảnh</label>
                <input type="file" id="imageFile" name="imageFile" accept="image/*">
                <div class="file-info">
                    <small>Chấp nhận: JPG, PNG, GIF. Tối đa 5MB</small>
                </div>
                <div class="error-message" id="imageFileError"></div>

                <!-- Image Preview -->
                <div id="imagePreview" class="image-preview" style="display: none;">
                    <img id="previewImg" src="" alt="Preview">
                    <button type="button" id="removeImageBtn" class="remove-image-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="redirectUrl">URL chuyển hướng</label>
                <input type="url" id="redirectUrl" name="redirectUrl"
                       placeholder="https://example.com (tùy chọn)">
                <div class="error-message" id="redirectUrlError"></div>
            </div>
        </form>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-primary" id="saveRoomTypeBtn">
                <i class="fas fa-save"></i> <span id="saveBtnText">Lưu</span>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteModal">
    <div class="modal-content delete-modal">
        <div class="modal-header">
            <h2><i class="fas fa-exclamation-triangle text-danger"></i> Xác nhận xóa</h2>
            <button class="modal-close" id="closeDeleteModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
            <p>Bạn có chắc chắn muốn xóa loại phòng <strong id="deleteRoomTypeName"></strong> không?</p>
            <p class="text-danger"><i class="fas fa-warning"></i> Hành động này không thể hoàn tác!</p>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i> Xóa
            </button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Đang tải dữ liệu...</p>
    </div>
</div>

<style>
    /* Ensure modal is properly displayed */
    .modal {
        z-index: 99999 !important;
        align-items: flex-start !important;
        justify-content: center !important;
        padding: 20px !important;
        padding-top: 10px !important;
        padding-bottom: 60px !important;
        overflow-y: auto !important;
    }

    .modal-content {
        max-height: 80vh !important;
        overflow: visible !important;
        margin: 0 auto 50px auto !important;
        position: relative !important;
        top: 0 !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .modal-body {
        max-height: calc(80vh - 160px) !important;
        overflow-y: auto !important;
        flex: 1 !important;
    }

    .modal-footer {
        position: sticky !important;
        bottom: 0 !important;
        background: #f8fafc !important;
        border-top: 1px solid #e5e7eb !important;
        z-index: 100 !important;
        flex-shrink: 0 !important;
        margin-top: auto !important;
    }

    /* Desktop specific */
    @@media (min-width: 769px) {
        .modal {
            align-items: flex-start !important;
            justify-content: center !important;
            padding: 30px !important;
            padding-top: 20px !important;
            padding-bottom: 80px !important;
        }

        .modal-content {
            width: 600px !important;
            max-width: 90vw !important;
            max-height: 75vh !important;
            margin: 0 auto 50px auto !important;
        }

        .modal-body {
            max-height: calc(75vh - 160px) !important;
        }
    }

    /* Mobile specific fixes */
    @@media (max-width: 768px) {
        .modal {
            align-items: flex-start !important;
            justify-content: center !important;
            padding: 15px !important;
            padding-top: 10px !important;
            padding-bottom: 50px !important;
        }

        .modal-content {
            margin: 0 auto !important;
            width: 100% !important;
            max-width: none !important;
            max-height: 82vh !important;
        }

        .modal-body {
            max-height: calc(82vh - 160px) !important;
        }

        .modal-footer {
            flex-direction: column !important;
            gap: 10px !important;
        }

            .modal-footer .btn {
                width: 100% !important;
                min-height: 50px !important;
                font-size: 16px !important;
            }
    }

    @@media (max-width: 480px) {
        .modal {
            padding: 10px !important;
            padding-top: 5px !important;
            padding-bottom: 40px !important;
        }

        .modal-content {
            max-height: 85vh !important;
        }

        .modal-body {
            max-height: calc(85vh - 140px) !important;
        }

        .modal-footer .btn {
            min-height: 52px !important;
            font-weight: 700 !important;
        }
    }

    .image-preview {
        position: relative;
        margin-top: 15px;
        border-radius: 8px;
    }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }

    .remove-image-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 24px;
        height: 24px;
        background: #ef4444;
        color: white;
        border: none;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
    }

        .remove-image-btn:hover {
            background: #dc2626;
        }

    /* Pagination styles */
    .pagination-info {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .items-per-page {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
    }

        .items-per-page label {
            margin: 0;
            font-weight: 500;
            color: #6b7280;
        }

        .items-per-page select {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            font-size: 14px;
            min-width: 60px;
        }

            .items-per-page select:focus {
                outline: none;
                border-color: #4a90a4;
                box-shadow: 0 0 0 2px rgba(74, 144, 164, 0.1);
            }

        .items-per-page span {
            color: #6b7280;
        }

    @@media (max-width: 768px) {
        .pagination-info {
            justify-content: center;
            gap: 15px;
        }

        .items-per-page {
            font-size: 13px;
        }
    }
</style>

<link href="~/css/roomtype-management.css?@DateTime.Now" rel="stylesheet">
<script src="~/js/roomtype-management.js?@DateTime.Now"></script> 