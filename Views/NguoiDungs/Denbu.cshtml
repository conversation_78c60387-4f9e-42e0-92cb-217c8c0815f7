@{
    ViewData["Title"] = "Quản lý Đền bù";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}
<link href="~/css/denbu.css?v=@DateTime.Now" rel="stylesheet" />
@section Modals {
    <!-- Add/Edit Modal -->
    <div class="modal" id="compensationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">
                    <i class="fas fa-file-invoice-dollar"></i> 
                    <span id="modalTitleText">Thêm đền bù</span>
                </h2>
                <button class="modal-close" id="closeModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="compensationForm" class="modal-body">
                <input type="hidden" id="compensationId" name="compensationId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="motelId">Nhà trọ <span class="required">*</span></label>
                        <select id="motelId" name="motelId" required>
                            <option value="">Chọn nhà trọ</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="roomId">Phòng <span class="required">*</span></label>
                        <select id="roomId" name="roomId" required disabled>
                            <option value="">Chọn phòng</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="contractId">Hợp đồng <span class="required">*</span></label>
                    <select id="contractId" name="maHopDong" required disabled>
                        <option value="">Chọn hợp đồng</option>
                    </select>
                    <div class="error-message" id="contractIdError"></div>
                </div>
                
                <div class="form-group">
                    <label for="noiDung">Nội dung <span class="required">*</span></label>
                    <textarea id="noiDung" name="noiDung" rows="4" required placeholder="Mô tả chi tiết về khoản đền bù..."></textarea>
                    <div class="error-message" id="noiDungError"></div>
                </div>

                <div class="form-group">
                    <label for="soTien">Số tiền (VNĐ) <span class="required">*</span></label>
                    <input type="number" id="soTien" name="soTien" required min="0" step="1000" placeholder="Nhập số tiền đền bù">
                    <div class="error-message" id="soTienError"></div>
                </div>
                <div class="form-group">
                    <label for="hinhanh">Hình ảnh minh chứng</label>
                    <input type="file" id="hinhanh" name="hinhanh" accept="image/*">
                    <div class="file-info">
                        <small>Chấp nhận: JPG, PNG, GIF. Tối đa 5MB</small>
                    </div>
                    <div class="error-message" id="hinhanhError"></div>
                    <div id="imagePreviewContainer" style="display:none; margin-top:10px;">
                        <img id="imagePreview" src="#" alt="Preview" style="max-width: 100%; max-height: 200px; border: 1px solid #ccc; border-radius: 6px;" />
                        <button type="button" id="removeImageBtn" class="btn btn-sm btn-danger" style="margin-top:5px;">Xóa ảnh</button>
                    </div>
                </div>
            </form>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                    <i class="fas fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-primary" id="saveBtn">
                    <i class="fas fa-save"></i> <span id="saveBtnText">Lưu</span>
                </button>
            </div>
        </div>
    </div>

    <!-- View Modal -->
    <div class="modal" id="viewModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-info-circle"></i> Chi tiết đền bù</h2>
                <button class="modal-close" id="closeViewModalBtn"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body" id="viewModalBody">
                <!-- Details will be injected here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="okViewModalBtn">
                    <i class="fas fa-check"></i> OK
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="deleteModal">
        <div class="modal-content delete-modal">
            <div class="modal-header">
                <h2><i class="fas fa-exclamation-triangle text-danger"></i> Xác nhận xóa</h2>
                <button class="modal-close" id="closeDeleteModalBtn"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa mục đền bù <strong id="deleteCompensationId"></strong> không?</p>
                <p class="text-danger"><i class="fas fa-warning"></i> Hành động này không thể hoàn tác.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelDeleteBtn"><i class="fas fa-times"></i> Hủy</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn"><i class="fas fa-trash"></i> Xóa</button>
            </div>
        </div>
    </div>
}

<div class="compensation-management">
    <div class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1><i class="fas fa-file-invoice-dollar"></i> Quản lý Đền bù</h1>
                <p>Thêm, sửa, xóa và quản lý các khoản đền bù phát sinh</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" id="addBtn">
                    <i class="fas fa-plus"></i> Thêm đền bù
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <div class="filters-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Tìm kiếm theo nội dung, mã hợp đồng...">
            </div>
            
            <div class="filter-group">
                <select id="contractFilter">
                    <option value="">Tất cả hợp đồng</option>
                </select>
                <button class="btn btn-secondary" id="clearFiltersBtn">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Table Section -->
    <div class="table-section">
        <div class="table-container">
            <div class="table-responsive">
                <table class="custom-table">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Hợp đồng & Phòng</th>
                            <th>Nội dung</th>
                            <th>Số tiền</th>
                            <th>Ngày tạo</th>
                            <th>Hình ảnh</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Cards -->
        <div class="mobile-cards" id="mobileCards">
            <!-- Mobile cards will be loaded here -->
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo"></span>
                <div class="items-per-page">
                    <label for="itemsPerPage">Hiển thị:</label>
                    <select id="itemsPerPage">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                    </select>
                    <span>mục/trang</span>
                </div>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-outline" id="prevPageBtn" disabled><i class="fas fa-chevron-left"></i> Trước</button>
                <div class="page-numbers" id="pageNumbers"></div>
                <button class="btn btn-outline" id="nextPageBtn" disabled>Sau <i class="fas fa-chevron-right"></i></button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Đang tải dữ liệu...</p>
    </div>
</div>

@section Scripts {


<script>
$(document).ready(function() {
    // --- STATE MANAGEMENT ---
    let allCompensations = [];
    let allContracts = [];
    let allMotels = [];
    let allRooms = [];
    let currentPage = 1;
    let itemsPerPage = 10;
    let currentEditId = null;

    // --- DOM ELEMENTS ---
    const $loadingOverlay = $('#loadingOverlay');
    const $tableBody = $('#tableBody');
    const $paginationInfo = $('#paginationInfo');
    const $pageNumbers = $('#pageNumbers');
    const $prevPageBtn = $('#prevPageBtn');
    const $nextPageBtn = $('#nextPageBtn');
    const $searchInput = $('#searchInput');
    const $contractFilter = $('#contractFilter');
    const $itemsPerPageSelect = $('#itemsPerPage');

    // Cascading dropdowns in modal
    const $motelSelect = $('#motelId');
    const $roomSelect = $('#roomId');
    const $contractSelect = $('#contractId');

    // Modals
    const $compensationModal = $('#compensationModal');
    const $deleteModal = $('#deleteModal');
    const $viewModal = $('#viewModal');
    const $modalTitleText = $('#modalTitleText');
    const $saveBtnText = $('#saveBtnText');
    const $compensationForm = $('#compensationForm');

    // --- DATA FETCHING ---
    function showLoading() { $loadingOverlay.show(); }
    function hideLoading() { $loadingOverlay.hide(); }

    async function fetchData() {
        showLoading();
        try {
            const [compensationsRes, contractsRes, motelsRes] = await Promise.all([
                fetch('/api/Denbu/get-denbu'),
                fetch('/api/Contract/get-contracts'),
                fetch('/api/Motel/get-list-motel')
            ]);

            const compensationsData = await compensationsRes.json();
            const contractsData = await contractsRes.json();
            const motelsData = await motelsRes.json();

            if (compensationsData.success) {
                allCompensations = compensationsData.data;
            } else {
                console.error("Failed to load compensations:", compensationsData.message);
            }

            if (contractsData.success) {
                allContracts = contractsData.data;
                populateContractFilters();
            } else {
                console.error("Failed to load contracts:", contractsData.message);
            }
            
            if (motelsData && Array.isArray(motelsData)) { // API returns an array directly
                allMotels = motelsData;
            } else if(motelsData.success) { // Or returns an ApiResponse object
                 allMotels = motelsData.data;
            } else {
                console.error("Failed to load motels:", motelsData.message);
            }

            render();
        } catch (error) {
            console.error('Failed to fetch data:', error);
            showAlert('Không thể tải dữ liệu. Vui lòng thử lại.', 'danger');
        } finally {
            hideLoading();
        }
    }

    // --- RENDERING ---
    function render() {
        const filteredData = getFilteredData();
        renderTable(filteredData);
        renderMobileCards(filteredData);
        renderPagination(filteredData.length);
    }

    function getFilteredData() {
        const contractId = $contractFilter.val();

        return allCompensations.filter(item => {
            const contract = allContracts.find(c => c.contractId == item.maHopDong);
            const searchTerm = $searchInput.val().toLowerCase();

            const matchesSearch = searchTerm === '' ||
                item.noiDung.toLowerCase().includes(searchTerm) ||
                item.maHopDong.toString().includes(searchTerm) ||
                (contract?.room?.tenPhong?.toLowerCase()?.includes(searchTerm)) ||
                (contract?.room?.motel?.tenNhaTro?.toLowerCase()?.includes(searchTerm));
            
            const matchesContract = contractId === '' || item.maHopDong == contractId;

            return matchesSearch && matchesContract;
        });
    }

    function renderTable(data) {
        $tableBody.empty();
        if (data.length === 0) {
            $tableBody.html('<tr><td colspan="7" class="text-center">Không tìm thấy dữ liệu phù hợp.</td></tr>');
            return;
        }

        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageData = data.slice(startIndex, endIndex);

        pageData.forEach((item, index) => {
            const contract = allContracts.find(c => c.contractId == item.maHopDong);
            const roomName = contract?.room?.tenPhong;
            const motelName = contract?.room?.motel?.tenNhaTro;
            const roomInfo = roomName && motelName ? `${roomName} (${motelName})` : (roomName || 'Không xác định');
            const imageHtml = item.base64
                ? `<img src="data:image/jpeg;base64,${item.base64}" alt="Ảnh minh chứng" style="max-width:60px;max-height:60px;border-radius:4px;object-fit:cover;" />`
                : '<span style="color:#aaa;font-size:12px;">Không có ảnh</span>';
            const row = `
                <tr>
                    <td>${startIndex + index + 1}</td>
                    <td><strong>#${item.maHopDong}</strong><br><small>${roomInfo}</small></td>
                    <td>${item.noiDung}</td>
                    <td>${item.soTien.toLocaleString('vi-VN')} VNĐ</td>
                    <td>${new Date(item.ngayTao).toLocaleDateString('vi-VN')}</td>
                    <td>${imageHtml}</td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-info btn-sm" onclick="window.viewItem(${item.maDenBu})" title="Xem"><i class="fas fa-eye"></i></button>
                            <button class="btn btn-primary btn-sm" onclick="window.editItem(${item.maDenBu})" title="Sửa"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-danger btn-sm" onclick="window.deleteItem(${item.maDenBu})" title="Xóa"><i class="fas fa-trash"></i></button>
                        </div>
                    </td>
                </tr>
            `;
            $tableBody.append(row);
        });
    }

    function renderMobileCards(data) {
        const $mobileCardsContainer = $('#mobileCards');
        $mobileCardsContainer.empty();
        if (data.length === 0) {
            return; // Table already shows the 'no data' message
        }

        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageData = data.slice(startIndex, endIndex);

        pageData.forEach(item => {
            const contract = allContracts.find(c => c.contractId == item.maHopDong);
            const roomName = contract?.room?.tenPhong || 'N/A';
            const imageHtml = item.base64
                ? `<img src="data:image/jpeg;base64,${item.base64}" alt="Ảnh minh chứng" style="max-width:60px;max-height:60px;border-radius:4px;object-fit:cover;display:block;margin-bottom:6px;" />`
                : '';
            const card = `
                <div class="compensation-card">
                    <div class="card-header">
                        <strong>HĐ #${item.maHopDong} - ${roomName}</strong>
                        <div class="card-actions">
                            <button class="btn btn-info btn-sm" onclick="window.viewItem(${item.maDenBu})" title="Xem"><i class="fas fa-eye"></i></button>
                            <button class="btn btn-primary btn-sm" onclick="window.editItem(${item.maDenBu})" title="Sửa"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-danger btn-sm" onclick="window.deleteItem(${item.maDenBu})" title="Xóa"><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                    <div class="card-body">
                        ${imageHtml}
                        <p><strong>Nội dung:</strong> ${item.noiDung}</p>
                        <p><strong>Số tiền:</strong> ${item.soTien.toLocaleString('vi-VN')} VNĐ</p>
                        <p><strong>Ngày tạo:</strong> ${new Date(item.ngayTao).toLocaleDateString('vi-VN')}</p>
                    </div>
                </div>
            `;
            $mobileCardsContainer.append(card);
        });
    }

    function renderPagination(totalItems) {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        
        $paginationInfo.text(`Hiển thị ${totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0} - ${Math.min(currentPage * itemsPerPage, totalItems)} của ${totalItems} mục`);
        
        $pageNumbers.empty();
        for (let i = 1; i <= totalPages; i++) {
            const btn = `<button class="btn btn-outline page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">${i}</button>`;
            $pageNumbers.append(btn);
        }

        $prevPageBtn.prop('disabled', currentPage === 1);
        $nextPageBtn.prop('disabled', currentPage === totalPages || totalPages === 0);
    }

    function populateContractFilters() {
        $contractFilter.empty().append('<option value="">Tất cả hợp đồng</option>');
        const modalSelect = $('#contractId').empty().append('<option value="">Chọn hợp đồng</option>');
        
        allContracts.forEach(c => {
            const room = c.room ? c.room.tenPhong : 'N/A';
            const option = `<option value="${c.contractId}">#${c.contractId} - ${room}</option>`;
            $contractFilter.append(option);
            modalSelect.append(option);
        });
    }

    // --- EVENT HANDLERS ---
    $searchInput.on('input', () => { currentPage = 1; render(); });
    $contractFilter.on('change', () => { currentPage = 1; render(); });
    $itemsPerPageSelect.on('change', function() {
        itemsPerPage = parseInt($(this).val());
        currentPage = 1;
        render();
    });

    $('#clearFiltersBtn').on('click', () => {
        $searchInput.val('');
        $contractFilter.val('');
        currentPage = 1;
        render();
    });

    $(document).on('click', '.page-btn', function() {
        currentPage = parseInt($(this).data('page'));
        render();
    });

    $prevPageBtn.on('click', () => { if (currentPage > 1) { currentPage--; render(); } });
    $nextPageBtn.on('click', () => {
        const totalPages = Math.ceil(getFilteredData().length / itemsPerPage);
        if (currentPage < totalPages) { currentPage++; render(); }
    });

    // --- CASCADING DROPDOWN LOGIC ---
    function populateMotelDropdown() {
        $motelSelect.empty().append('<option value="">Chọn nhà trọ</option>');
        allMotels.forEach(motel => {
            $motelSelect.append(`<option value="${motel.maNhaTro}">${motel.tenNhaTro}</option>`);
        });
    }
    
    async function updateRoomDropdown(motelId) {
        $roomSelect.prop('disabled', true).empty().append('<option value="">Đang tải...</option>');
        $contractSelect.prop('disabled', true).empty().append('<option value="">Vui lòng chọn phòng</option>');

        if (!motelId) {
            $roomSelect.empty().append('<option value="">Vui lòng chọn nhà trọ</option>');
            return;
        }

        try {
            // Lấy tất cả phòng của nhà trọ (không chỉ phòng trống)
            const response = await fetch(`/api/Room/get-room-by-nha-tro/${motelId}`);
            const result = await response.json();
            
            if (result.success && result.data && result.data.length > 0) {
                allRooms = result.data;
                $roomSelect.empty().append('<option value="">Chọn phòng</option>');
                allRooms.forEach(room => {
                    $roomSelect.append(`<option value="${room.maPhong}">${room.tenPhong}</option>`);
                });
                $roomSelect.prop('disabled', false);
            } else {
                // Fallback: thử endpoint khác
                const fallbackResponse = await fetch(`/api/Room/get-room-trong-by-nha-tro/${motelId}`);
                const fallbackResult = await fallbackResponse.json();
                if (fallbackResult.success && fallbackResult.data.phong && fallbackResult.data.phong.length > 0) {
                    allRooms = fallbackResult.data.phong;
                    $roomSelect.empty().append('<option value="">Chọn phòng</option>');
                    allRooms.forEach(room => {
                        $roomSelect.append(`<option value="${room.maPhong}">${room.tenPhong}</option>`);
                    });
                    $roomSelect.prop('disabled', false);
                } else {
                    $roomSelect.empty().append('<option value="">Không có phòng nào</option>');
                }
            }
        } catch (error) {
            console.error('Failed to fetch rooms:', error);
            $roomSelect.empty().append('<option value="">Lỗi tải phòng</option>');
        }
    }

    async function updateContractDropdown(roomId) {
        $contractSelect.prop('disabled', true).empty().append('<option value="">Đang tải...</option>');

        if (!roomId) {
            $contractSelect.empty().append('<option value="">Vui lòng chọn phòng</option>');
            return;
        }

        try {
            // Lấy tất cả hợp đồng của phòng (bao gồm cả đã hoàn thành)
                    const response = await fetch(`/api/Contract/get-contract-by-room-id/${roomId}`);
            const result = await response.json();
            
            if (result.success && result.data && result.data.length > 0) {
                $contractSelect.empty().append('<option value="">Chọn hợp đồng</option>');
                result.data.forEach(contract => {
                    const status = contract.isCompleted ? ' (Đã hoàn thành)' : ' (Đang hiệu lực)';
                    $contractSelect.append(`<option value="${contract.contractId}">Hợp đồng #${contract.contractId}${status}</option>`);
                });
                $contractSelect.prop('disabled', false);
            } else {
                // Fallback: thử endpoint cũ
                        const fallbackResponse = await fetch(`/api/Contract/get-contract-by-room-id/${roomId}`);
                const fallbackResult = await fallbackResponse.json();
                if (fallbackResult.success && fallbackResult.data) {
                    const contract = fallbackResult.data;
                    $contractSelect.empty().append('<option value="">Chọn hợp đồng</option>');
                    const status = contract.isCompleted ? ' (Đã hoàn thành)' : ' (Đang hiệu lực)';
                    $contractSelect.append(`<option value="${contract.contractId}">Hợp đồng #${contract.contractId}${status}</option>`);
                    $contractSelect.prop('disabled', false);
                } else {
                    $contractSelect.empty().append('<option value="">Không có hợp đồng cho phòng này</option>');
                }
            }
        } catch (error) {
            console.error('Failed to fetch contract:', error);
            $contractSelect.empty().append('<option value="">Lỗi tải hợp đồng</option>');
        }
    }

    $motelSelect.on('change', function() {
        updateRoomDropdown($(this).val());
    });

    $roomSelect.on('change', function() {
        updateContractDropdown($(this).val());
    });

    // --- MODAL & CRUD ACTIONS ---
    function openModal(isEdit = false) {
        $compensationForm[0].reset();
        $('.error-message').text('');
        // Reset file input & preview khi mở modal thêm mới
        $('#hinhanh').val('');
        $('#imagePreviewContainer').hide();
        $('#imagePreview').attr('src', '#');
        if (isEdit) {
            $modalTitleText.text('Sửa đền bù');
            $saveBtnText.text('Cập nhật');
        } else {
            $modalTitleText.text('Thêm đền bù');
            $saveBtnText.text('Lưu');
            currentEditId = null;
            // Populate motels when adding
            populateMotelDropdown();
            $roomSelect.prop('disabled', true).empty().append('<option value="Chọn nhà trọ trước">');
            $contractSelect.prop('disabled', true).empty().append('<option value="Chọn phòng trước">');
        }
        $compensationModal.fadeIn();
    }

    function closeModal($modal) {
        $modal.fadeOut();
    }

    $('#addBtn').on('click', () => openModal(false));
    $('#closeModalBtn, #cancelModalBtn').on('click', () => closeModal($compensationModal));
    
    window.editItem = async function(id) {
        const item = allCompensations.find(c => c.maDenBu == id);
        if (!item) {
            showAlert('Không tìm thấy thông tin đền bù.', 'warning');
            return;
        }

        const contract = allContracts.find(c => c.contractId == item.maHopDong);
        if (!contract || !contract.room) {
            showAlert('Không thể tải thông tin chi tiết cho hợp đồng này.', 'warning');
            return;
        }

        openModal(true);
        currentEditId = id;
        // Hiển thị preview ảnh nếu có
        if (item.base64) {
            $('#imagePreview').attr('src', 'data:image/jpeg;base64,' + item.base64);
            $('#imagePreviewContainer').show();
        } else {
            $('#imagePreview').attr('src', '#');
            $('#imagePreviewContainer').hide();
        }
        $('#hinhanh').val(''); // reset input file, chỉ gửi ảnh mới nếu chọn lại
        try {
            // 1. Populate và chọn nhà trọ
            populateMotelDropdown();
            // Tìm mã nhà trọ từ thông tin phòng
            const motelId = contract.room.maNhaTro || contract.room.nhaTro?.maNhaTro;
            if (motelId) {
                $motelSelect.val(motelId);
                // 2. Load và chọn phòng
                await updateRoomDropdown(motelId);
                $roomSelect.val(contract.room.maPhong);
                // 3. Load và chọn hợp đồng  
                await updateContractDropdown(contract.room.maPhong);
                $contractSelect.val(item.maHopDong);
            } else {
                // Fallback: nếu không có thông tin nhà trọ, load tất cả hợp đồng
                populateContractFilters();
                $contractSelect.val(item.maHopDong);
            }
            // 4. Set các field khác
            $('#compensationId').val(item.maDenBu);
            $('#noiDung').val(item.noiDung);
            $('#soTien').val(item.soTien);
        } catch (error) {
            console.error('Error loading edit data:', error);
            showAlert('Có lỗi khi tải dữ liệu chỉnh sửa.', 'danger');
        }
    };

    window.viewItem = function(id) {
        const item = allCompensations.find(c => c.maDenBu == id);
        if (item) {
             const contract = allContracts.find(c => c.contractId == item.maHopDong);
             const roomName = contract?.room?.tenPhong;
             const motelName = contract?.room?.motel?.tenNhaTro;
             const roomInfo = roomName && motelName ? `${roomName} (${motelName})` : (roomName || 'Không xác định');
            const imageHtml = item.base64
                ? `<div style='margin-bottom:10px;'><img src="data:image/jpeg;base64,${item.base64}" alt="Ảnh minh chứng" style="max-width:100%;max-height:250px;border-radius:6px;object-fit:contain;box-shadow:0 2px 8px #0001;" /></div>`
                : '';
            const detailsHtml = `
                ${imageHtml}
                <p><strong>Mã đền bù:</strong> ${item.maDenBu}</p>
                <p><strong>Hợp đồng:</strong> #${item.maHopDong} - ${roomInfo}</p>
                <p><strong>Ngày tạo:</strong> ${new Date(item.ngayTao).toLocaleDateString('vi-VN')}</p>
                <p><strong>Số tiền:</strong> ${item.soTien.toLocaleString('vi-VN')} VNĐ</p>
                <hr>
                <p><strong>Nội dung:</strong></p>
                <p>${item.noiDung}</p>
            `;
            $('#viewModalBody').html(detailsHtml);
            $viewModal.fadeIn();
        }
    };
    $('#closeViewModalBtn, #okViewModalBtn').on('click', () => closeModal($viewModal));

    window.deleteItem = function(id) {
        $('#deleteCompensationId').text(`#${id}`);
        $deleteModal.fadeIn();
        $('#confirmDeleteBtn').off('click').on('click', async function() {
            showLoading();
            try {
                const response = await fetch(`/api/Denbu/delete-denbu/${id}`, { method: 'DELETE' });
                const result = await response.json();
                if (result.success) {
                    showAlert('Xóa đền bù thành công.', 'success');
                    fetchData(); // Refetch all data
                } else {
                    showAlert(result.message || 'Có lỗi xảy ra khi xóa.', 'danger');
                }
            } catch (error) {
                showAlert('Không thể kết nối đến máy chủ.', 'danger');
            } finally {
                hideLoading();
                closeModal($deleteModal);
            }
        });
    };
    $('#closeDeleteModalBtn, #cancelDeleteBtn').on('click', () => closeModal($deleteModal));


    // --- IMAGE UPLOAD & PREVIEW ---
    $('#hinhanh').on('change', function() {
        const file = this.files[0];
        const $error = $('#hinhanhError');
        const $previewContainer = $('#imagePreviewContainer');
        const $preview = $('#imagePreview');
        $error.text('');
        $previewContainer.hide();
        $preview.attr('src', '#');
        if (file) {
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            const maxSize = 5 * 1024 * 1024; // 5MB
            if (!allowedTypes.includes(file.type.toLowerCase())) {
                $error.text('Chỉ chấp nhận file ảnh JPG, PNG, GIF.');
                this.value = '';
                return;
            }
            if (file.size > maxSize) {
                $error.text('Kích thước ảnh không được vượt quá 5MB.');
                this.value = '';
                return;
            }
            // Hiển thị preview
            const reader = new FileReader();
            reader.onload = function(e) {
                $preview.attr('src', e.target.result);
                $previewContainer.show();
            };
            reader.readAsDataURL(file);
        }
    });
    $('#removeImageBtn').on('click', function() {
        $('#hinhanh').val('');
        $('#imagePreviewContainer').hide();
        $('#imagePreview').attr('src', '#');
    });

    $('#saveBtn').on('click', async function() {
        // Simple validation
        let isValid = true;
        if (!$('#contractId').val()) {
            $('#contractIdError').text('Vui lòng chọn hợp đồng.'); isValid = false;
        } else { $('#contractIdError').text(''); }

        if (!$('#noiDung').val().trim()) {
            $('#noiDungError').text('Vui lòng nhập nội dung.'); isValid = false;
        } else { $('#noiDungError').text(''); }
        
        if (!$('#soTien').val() || $('#soTien').val() < 0) {
            $('#soTienError').text('Vui lòng nhập số tiền hợp lệ.'); isValid = false;
        } else { $('#soTienError').text(''); }

        // Validate file nếu có chọn
        const file = $('#hinhanh')[0].files[0];
        const $error = $('#hinhanhError');
        $error.text('');
        if (file) {
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            const maxSize = 5 * 1024 * 1024; // 5MB
            if (!allowedTypes.includes(file.type.toLowerCase())) {
                $error.text('Chỉ chấp nhận file ảnh JPG, PNG, GIF.');
                isValid = false;
            }
            if (file.size > maxSize) {
                $error.text('Kích thước ảnh không được vượt quá 5MB.');
                isValid = false;
            }
        }
        if (!isValid) return;

        // Sử dụng FormData để gửi đúng chuẩn API backend
        const formData = new FormData();
        formData.append('maHopDong', $('#contractId').val());
        formData.append('noiDung', $('#noiDung').val());
        formData.append('soTien', $('#soTien').val());
        // Nếu có file mới thì gửi, nếu không thì giữ ảnh cũ (chỉ gửi khi chọn mới)
        if (file) formData.append('hinhanh', file);

        const isEdit = !!currentEditId;
        const url = isEdit ? `/api/Denbu/edit-denbu/${currentEditId}` : '/api/Denbu/add-denbu';
        const method = isEdit ? 'PUT' : 'POST';

        showLoading();
        try {
            const response = await fetch(url, {
                method: method,
                body: formData
            });
            const result = await response.json();

            if (result.success) {
                showAlert(isEdit ? 'Cập nhật thành công.' : 'Thêm mới thành công.', 'success');
                closeModal($compensationModal);
                fetchData(); // Refetch all data
                // Reset file input & preview
                $('#hinhanh').val('');
                $('#imagePreviewContainer').hide();
                $('#imagePreview').attr('src', '#');
            } else {
                let errorMessage = result.message || 'Có lỗi xảy ra.';
                showAlert(errorMessage, 'danger');
            }
        } catch (error) {
            showAlert('Không thể kết nối đến máy chủ.', 'danger');
        } finally {
            hideLoading();
        }
    });

    function showAlert(message, type = 'success') {
        const alert = $(`<div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>`);
        $('#alertContainer').append(alert);
        setTimeout(() => alert.fadeOut('slow', () => alert.remove()), 5000);
    }
    
    // --- INITIALIZATION ---
    fetchData();
});
</script>
} 