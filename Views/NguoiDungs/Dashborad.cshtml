﻿@{
    ViewData["Title"] = "Dashboard";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="dashboard-container">
    <!-- Enhanced Page Header -->
    <div class="page-header">
        <div class="page-title-wrapper">
            <h1 class="page-title">
                <i class="fas fa-chart-line"></i>
                Dashboard Tổng Quan
            </h1>
            <p class="page-subtitle">Thống kê và báo cáo hệ thống quản lý nhà trọ</p>
            <div class="page-breadcrumb">
                <span><i class="fas fa-home"></i> Trang chủ</span>
                <span class="separator">/</span>
                <span class="current">Dashboard</span>
            </div>
        </div>
        <div class="page-actions">
            <button class="btn btn-secondary" onclick="exportReport()">
                <i class="fas fa-download"></i>
                <PERSON><PERSON>t báo cáo
            </button>
            <button class="btn btn-primary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i>
                Làm mới
            </button>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="loading-container">
        <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <p>Đang tải dữ liệu...</p>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div id="dashboardContent" class="dashboard-content" style="display: none;">
        
        <!-- Enhanced Quick Stats Cards -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="stat-content">
                    <h3 id="soHopDong">0</h3>
                    <p>Hợp Đồng</p>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>Đang hoạt động</span>
                    </div>
                </div>
                <div class="stat-chart">
                    <canvas id="contractMiniChart"></canvas>
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-door-open"></i>
                </div>
                <div class="stat-content">
                    <h3 id="soPhong">0</h3>
                    <p>Tổng Phòng</p>
                    <div class="stat-breakdown">
                        <div class="breakdown-item available">
                            <span class="dot"></span>
                            <span>Trống: <strong id="sophongTrong">0</strong></span>
                        </div>
                        <div class="breakdown-item occupied">
                            <span class="dot"></span>
                            <span>Đã thuê: <strong id="sophongDaThue">0</strong></span>
                        </div>
                    </div>
                </div>
                <div class="stat-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="occupancyProgress"></div>
                    </div>
                    <span class="progress-text" id="occupancyText">0%</span>
                </div>
            </div>

            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3 id="soKhachHang">0</h3>
                    <p>Khách Hàng</p>
                    <div class="stat-trend positive">
                        <i class="fas fa-user-plus"></i>
                        <span>Đang thuê</span>
                    </div>
                </div>
                <div class="stat-chart">
                    <canvas id="customerMiniChart"></canvas>
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-content">
                    <h3 id="soNhaTro">0</h3>
                    <p>Nhà Trọ</p>
                    <div class="stat-trend positive">
                        <i class="fas fa-map-marker-alt"></i>
                        <span id="soTinh">0</span> Tỉnh/Thành
                    </div>
                </div>
                <div class="stat-chart">
                    <canvas id="propertyMiniChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Revenue and Analytics Section -->
        <div class="analytics-section">
            <div class="analytics-row">
                <!-- Enhanced Revenue Chart -->
                <div class="chart-card large">
                    <div class="chart-header">
                        <div class="chart-title">
                            <h3>
                                <i class="fas fa-chart-area"></i>
                                Doanh Thu & Tăng Trưởng
                            </h3>
                            <p class="chart-subtitle">Phân tích doanh thu theo thời gian với tỷ lệ tăng trưởng</p>
                        </div>
                      
                    </div>
                    <div class="chart-body">
                        <div class="revenue-summary">
                            <div class="summary-item">
                                <span class="label">Hôm nay:</span>
                                <span class="value" id="revenueToday">0 VNĐ</span>
                                <span class="change positive" id="todayChange">+0%</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Tuần này:</span>
                                <span class="value" id="tuannay">0 VNĐ</span>
                                <span class="change positive" id="tuannayChange">+0%</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Tháng này:</span>
                                <span class="value" id="revenueMonth">0 VNĐ</span>
                                <span class="change positive" id="monthChange">+0%</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">Năm này:</span>
                                <span class="value" id="namnay">0 VNĐ</span>
                                <span class="change positive" id="namnayChange">+0%</span>
                            </div>
                        </div>
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>

                <!-- Enhanced Room Analytics -->
                <div class="chart-card medium">
                    <div class="chart-header">
                        <h3>
                            <i class="fas fa-chart-pie"></i>
                            Phân Tích Phòng
                        </h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="occupancyChart"></canvas>
                        <div class="occupancy-details">
                            <div class="detail-item">
                                <div class="detail-icon occupied">
                                    <i class="fas fa-home"></i>
                                </div>
                                <div class="detail-content">
                                    <h4 id="occupiedCount">0</h4>
                                    <p>Phòng đã thuê</p>
                                    <span class="percentage" id="occupiedPercent">0%</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-icon available">
                                    <i class="fas fa-door-open"></i>
                                </div>
                                <div class="detail-content">
                                    <h4 id="availableCount">0</h4>
                                    <p>Phòng trống</p>
                                    <span class="percentage" id="availablePercent">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Utility and System Stats Row -->
            <div class="analytics-row">
                <!-- Enhanced Utility Chart -->
                <div class="chart-card medium">
                    <div class="chart-header">
                        <h3>
                            <i class="fas fa-bolt"></i>
                            Tiền Điện & Nước
                        </h3>
                        <div class="utility-summary">
                            <div class="utility-item electric">
                                <i class="fas fa-bolt"></i>
                                <span>Điện: <strong id="electricTotal">0 VNĐ</strong></span>
                            </div>
                            <div class="utility-item water">
                                <i class="fas fa-tint"></i>
                                <span>Nước: <strong id="waterTotal">0 VNĐ</strong></span>
                            </div>
                        </div>
                    </div>
                    <div class="chart-body">
                        <canvas id="utilityChart"></canvas>
                    </div>
                </div>

                <!-- Enhanced System Stats -->
                <div class="chart-card medium">
                    <div class="chart-header">
                        <h3>
                            <i class="fas fa-cogs"></i>
                            Thống Kê Hệ Thống
                        </h3>
                    </div>
                    <div class="chart-body">
                        <div class="system-grid">
                            <div class="system-item">
                                <div class="system-icon location">
                                    <i class="fas fa-map-marked-alt"></i>
                                </div>
                                <div class="system-content">
                                    <h4 id="soTinhDetail">0</h4>
                                    <p>Tỉnh/Thành phố</p>
                                    <small id="soKhuVuc">0 khu vực</small>
                                </div>
                            </div>
                            <div class="system-item">
                                <div class="system-icon category">
                                    <i class="fas fa-layer-group"></i>
                                </div>
                                <div class="system-content">
                                    <h4 id="soLoaiPhong">0</h4>
                                    <p>Loại phòng</p>
                                    <small>Đa dạng</small>
                                </div>
                            </div>
                            <div class="system-item">
                                <div class="system-icon banking">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="system-content">
                                    <h4 id="soBank">0</h4>
                                    <p>Ngân hàng</p>
                                    <small>Liên kết</small>
                                </div>
                            </div>
                            <div class="system-item">
                                <div class="system-icon message">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div class="system-content">
                                    <h4 id="soTinNhan">0</h4>
                                    <p>Tin nhắn</p>
                                    <small id="soBanner">0 banner</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Recent Activity with Real Data -->
        <div class="activity-section">
            <div class="activity-card">
                <div class="activity-header">
                    <h3>
                        <i class="fas fa-clock"></i>
                        Hoạt Động Gần Đây
                    </h3>
                    <div class="activity-controls">
                        <select id="activityFilter" class="form-control">
                            <option value="all">Tất cả</option>
                            <option value="contracts">Hợp đồng</option>
                            <option value="payments">Thanh toán</option>
                            <option value="maintenance">Bảo trì</option>
                        </select>
                        <button class="btn btn-sm btn-outline" onclick="refreshActivity()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="activity-body">
                    <div id="activityTimeline" class="activity-timeline">
                        <!-- Activities will be loaded here -->
                    </div>
                    <div class="activity-footer">
                        <button class="btn btn-outline" onclick="loadMoreActivities()">
                            <i class="fas fa-plus"></i>
                            Xem thêm hoạt động
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Panel -->
        <div class="quick-actions-section">
            <div class="quick-actions-card">
                <div class="actions-header">
                    <h3>
                        <i class="fas fa-bolt"></i>
                        Thao Tác Nhanh
                    </h3>
                </div>
                <div class="actions-grid">
                    <a href="#" class="action-item">
                        <div class="action-icon contract">
                            <i class="fas fa-file-signature"></i>
                        </div>
                        <span>Tạo hợp đồng mới</span>
                    </a>
                    <a href="#" class="action-item">
                        <div class="action-icon invoice">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <span>Xuất hóa đơn</span>
                    </a>
                    <a href="#" class="action-item">
                        <div class="action-icon customer">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <span>Thêm khách hàng</span>
                    </a>
                    <a href="#" class="action-item">
                        <div class="action-icon room">
                            <i class="fas fa-plus-square"></i>
                        </div>
                        <span>Thêm phòng mới</span>
                    </a>
                    <a href="#" class="action-item">
                        <div class="action-icon report">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span>Xem báo cáo</span>
                    </a>
                    <a href="#" class="action-item">
                        <div class="action-icon settings">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span>Cài đặt hệ thống</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let dashboardData = null;
        let charts = {};
        let activityData = [];

        // Enhanced dashboard initialization
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });

        async function initializeDashboard() {
            showLoading();
            try {
                await Promise.all([
                    loadDashboardData(),
                    loadRecentActivities()
                ]);
                hideLoading();
            } catch (error) {
                console.error('Dashboard initialization failed:', error);
                hideLoading();
                showError('Không thể khởi tạo dashboard: ' + error.message);
            }
        }

        async function loadDashboardData() {
            const response = await fetch('/api/Admin/Dashborad');
            const result = await response.json();
            
            if (result.success) {
                dashboardData = result.data;
                updateDashboard();
                createAllCharts();
            } else {
                throw new Error(result.message || 'Lỗi khi tải dữ liệu');
            }
        }

        async function loadRecentActivities() {
            try {
                const response = await fetch('/api/Admin/RecentActivities');
                const result = await response.json();
                
                if (result.success) {
                    activityData = result.data;
                    renderActivities();
                } else {
                    console.error('Error loading activities:', result.message);
                    // Fallback to sample data if API fails
                    activityData = [
                        {
                            id: 1,
                            type: 'contract',
                            icon: 'fas fa-handshake',
                            iconClass: 'success',
                            title: 'Hợp đồng mới được ký',
                            description: 'Hợp đồng thuê phòng đã được ký',
                            time: '2 giờ trước',
                            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
                        },
                        {
                            id: 2,
                            type: 'payment',
                            icon: 'fas fa-credit-card',
                            iconClass: 'info',
                            title: 'Thanh toán hóa đơn',
                            description: 'Hóa đơn đã được thanh toán đầy đủ',
                            time: '4 giờ trước',
                            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
                        }
                    ];
                    renderActivities();
                }
            } catch (error) {
                console.error('Error fetching activities:', error);
                // Fallback to sample data
                activityData = [];
                renderActivities();
            }
        }

        function renderActivities() {
            const timeline = document.getElementById('activityTimeline');
            timeline.innerHTML = '';

            activityData.forEach(activity => {
                const timelineItem = document.createElement('div');
                timelineItem.className = 'timeline-item';
                timelineItem.innerHTML = `
                    <div class="timeline-icon ${activity.iconClass}">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="timeline-content">
                        <h4>${activity.title}</h4>
                        <p>${activity.description}</p>
                        <div class="timeline-meta">
                            <span class="timeline-time">
                                <i class="fas fa-clock"></i>
                                ${activity.time}
                            </span>
                            <span class="timeline-type">${getActivityTypeLabel(activity.type)}</span>
                        </div>
                    </div>
                `;
                timeline.appendChild(timelineItem);
            });
        }

        function getActivityTypeLabel(type) {
            const labels = {
                'contract': 'Hợp đồng',
                'payment': 'Thanh toán',
                'maintenance': 'Bảo trì',
                'customer': 'Khách hàng'
            };
            return labels[type] || 'Khác';
        }

        function updateDashboard() {
            if (!dashboardData) return;

            // Update stat cards with animation
            animateCounter('soHopDong', dashboardData.soHopDong);
            animateCounter('soPhong', dashboardData.soPhong);
            animateCounter('soKhachHang', dashboardData.soKhachHang);
            animateCounter('soNhaTro', dashboardData.soNhaTro);
            animateCounter('sophongTrong', dashboardData.sophongTrong);
            animateCounter('sophongDaThue', dashboardData.sophongDaThue);
            animateCounter('soTinh', dashboardData.soTinh);
            animateCounter('soTinhDetail', dashboardData.soTinh);
            animateCounter('soKhuVuc', dashboardData.soKhuVuc);
            animateCounter('soLoaiPhong', dashboardData.soLoaiPhong);
            animateCounter('soBank', dashboardData.soBank);
            animateCounter('soTinNhan', dashboardData.soTinNhan);
            animateCounter('soBanner', dashboardData.soBanner);

            // Update occupancy progress
            const totalRooms = dashboardData.soPhong;
            const occupiedPercent = totalRooms > 0 ? ((dashboardData.sophongDaThue / totalRooms) * 100) : 0;
            const availablePercent = 100 - occupiedPercent;
            
            document.getElementById('occupancyProgress').style.width = occupiedPercent + '%';
            document.getElementById('occupancyText').textContent = occupiedPercent.toFixed(1) + '%';
            document.getElementById('occupiedPercent').textContent = occupiedPercent.toFixed(1) + '%';
            document.getElementById('availablePercent').textContent = availablePercent.toFixed(1) + '%';
            document.getElementById('occupiedCount').textContent = dashboardData.sophongDaThue;
            document.getElementById('availableCount').textContent = dashboardData.sophongTrong;

            // Update revenue summary
            document.getElementById('revenueToday').textContent = formatCurrency(dashboardData.homNay);
            document.getElementById('namnay').textContent = formatCurrency(dashboardData.namNay);
            document.getElementById('tuannay').textContent = formatCurrency(dashboardData.tuanNay);
            document.getElementById('revenueMonth').textContent = formatCurrency(dashboardData.thangNay);
            document.getElementById('todayChange').textContent = formatPercent(dashboardData.tyLeThayDoiHomNay);
            document.getElementById('tuannayChange').textContent = formatPercent(dashboardData.tyLeThayDoiTuanNay);
            document.getElementById('namnayChange').textContent = formatPercent(dashboardData.tyLeThayDoiNamNay);
            document.getElementById('monthChange').textContent = formatPercent(dashboardData.tyLeThayDoiThangNay);

            // Update utility totals
            document.getElementById('electricTotal').textContent = formatCurrency(dashboardData.dienthangnay);
            document.getElementById('waterTotal').textContent = formatCurrency(dashboardData.nuocthangnay);
        }

        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const startValue = 0;
            const duration = 1000;
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
                
                element.textContent = currentValue.toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        }

        function createAllCharts() {
            createRevenueChart();
            createOccupancyChart();
            createUtilityChart();
            createMiniCharts();
        }

        function createRevenueChart() {
            const ctx = document.getElementById('revenueChart').getContext('2d');
            
            if (charts.revenue) {
                charts.revenue.destroy();
            }

            const data = {
                labels: ['Hôm qua', 'Hôm nay', 'Tuần trước', 'Tuần này', 'Tháng trước', 'Tháng này'],
                datasets: [{
                    label: 'Doanh thu (VNĐ)',
                    data: [
                        dashboardData.homQua,
                        dashboardData.homNay,
                        dashboardData.tuanTruoc,
                        dashboardData.tuanNay,
                        dashboardData.thangTruoc,
                        dashboardData.thangNay
                    ],
                    backgroundColor: 'rgba(74, 144, 164, 0.1)',
                    borderColor: '#4a90a4',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#4a90a4',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            };

            charts.revenue = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#4a90a4',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function createOccupancyChart() {
            const ctx = document.getElementById('occupancyChart').getContext('2d');
            
            if (charts.occupancy) {
                charts.occupancy.destroy();
            }

            const data = {
                labels: ['Đã thuê', 'Còn trống'],
                datasets: [{
                    data: [dashboardData.sophongDaThue, dashboardData.sophongTrong],
                    backgroundColor: ['#4a90a4', '#e5e7eb'],
                    borderWidth: 0,
                    hoverBackgroundColor: ['#2c5f6f', '#d1d5db']
                }]
            };

            charts.occupancy = new Chart(ctx, {
                type: 'doughnut',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function createUtilityChart() {
            const ctx = document.getElementById('utilityChart').getContext('2d');
            
            if (charts.utility) {
                charts.utility.destroy();
            }

            const data = {
                labels: ['Tháng trước', 'Tháng này'],
                datasets: [
                    {
                        label: 'Tiền điện',
                        data: [dashboardData.dienthangtruoc, dashboardData.dienthangnay],
                        backgroundColor: '#f59e0b',
                        borderRadius: 8,
                        borderSkipped: false
                    },
                    {
                        label: 'Tiền nước',
                        data: [dashboardData.nuocthangtruoc, dashboardData.nuocthangnay],
                        backgroundColor: '#06b6d4',
                        borderRadius: 8,
                        borderSkipped: false
                    }
                ]
            };

            charts.utility = new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        function createMiniCharts() {
            // Create mini sparkline charts for stat cards
            createMiniChart('contractMiniChart', [10, 15, 12, 18, 20, 25, dashboardData.soHopDong]);
            createMiniChart('customerMiniChart', [50, 60, 55, 70, 80, 90, dashboardData.soKhachHang]);
            createMiniChart('propertyMiniChart', [5, 8, 6, 10, 12, 15, dashboardData.soNhaTro]);
        }

        function createMiniChart(canvasId, data) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array(data.length).fill(''),
                    datasets: [{
                        data: data,
                        borderColor: '#ffffff',
                        borderWidth: 2,
                        fill: false,
                        pointRadius: 0,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            });
        }

        // Utility functions
        function formatCurrency(value) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(value);
        }

        function formatPercent(value) {
            const sign = value >= 0 ? '+' : '';
            return sign + value.toFixed(1) + '%';
        }

        function showLoading() {
            document.getElementById('loadingState').style.display = 'flex';
            document.getElementById('dashboardContent').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'block';
        }

        function showError(message) {
            // Implement toast notification
            console.error(message);
        }

        function refreshDashboard() {
            initializeDashboard();
        }

        function refreshActivity() {
            loadRecentActivities();
        }

        function loadMoreActivities() {
            // Implement pagination for activities
            console.log('Loading more activities...');
        }

        async function exportReport() {
            const exportBtn = document.querySelector('button[onclick="exportReport()"]');
            const originalText = exportBtn.innerHTML;
            
            try {
                // Show loading state
                exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xuất...';
                exportBtn.disabled = true;

                // Call export API
                const response = await fetch('/api/Admin/ExportReport');
                
                if (response.ok) {
                    // Create download link
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `BaoCao_Dashboard_${new Date().toISOString().slice(0,10)}.html`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    // Show success message
                    showNotification('Xuất báo cáo thành công!', 'success');
                } else {
                    throw new Error('Lỗi khi xuất báo cáo');
                }
            } catch (error) {
                console.error('Export error:', error);
                showNotification('Không thể xuất báo cáo: ' + error.message, 'error');
            } finally {
                // Reset button
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;
            }
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                animation: slideIn 0.3s ease;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            
            // Set background color based on type
            switch(type) {
                case 'success':
                    notification.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                    break;
                case 'error':
                    notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
                    break;
                default:
                    notification.style.background = 'linear-gradient(135deg, #3b82f6, #2563eb)';
            }
            
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @@keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @@keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        function toggleRevenueView() {
            // Implement toggle between different revenue views
            console.log('Toggling revenue view...');
        }
    </script>
}