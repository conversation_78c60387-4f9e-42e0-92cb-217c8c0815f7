﻿@model Ql_NhaTro_jun.Models.NguoiDung

@{
    ViewData["Title"] = "Thông tin cá nhân";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="simple-profile">
    <div class="profile-header">
        <h2>Thông tin cá nhân</h2>
    </div>
    
    <div class="profile-content">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="success-msg">@TempData["SuccessMessage"]</div>
        }

        <form class="profile-form" novalidate>
            <input type="hidden" asp-for="MaNguoiDung" />

            <div class="form-group full-width">
                <label asp-for="HoTen">Họ và tên</label>
                <input asp-for="HoTen" type="text" placeholder="Nhập họ và tên đầy đủ của bạn" required />
            </div>

            <div class="form-group full-width">
                <label asp-for="MatKhau">Mật khẩu mới</label>
                <div class="password-field">
                    <input asp-for="MatKhau" type="password" placeholder="Để trống nếu không muốn thay đổi mật khẩu" />
                    <button type="button" class="show-pass" id="togglePassword">👁</button>
                </div>
                <small>Để trống nếu bạn không muốn thay đổi mật khẩu hiện tại</small>
            </div>

            <div class="form-row">
                <div class="form-group readonly">
                    <label>Số điện thoại</label>
                    <input value="@Model.SoDienThoai" readonly />
                </div>
                <div class="form-group readonly">
                    <label>Số Căn cước công dân</label>
                    <input value="@Model.so_cccd" readonly />
                </div>
                <div class="form-group readonly">
                    <label>Email</label>
                    <input value="@Model.Email" readonly />
                </div>
            </div>

            <button type="submit" class="save-btn">Lưu thay đổi</button>
        </form>
    </div>
</div>

<style>
.simple-profile {
    max-width: 700px;
    margin: 30px auto;
    padding: 0;
    background: linear-gradient(145deg, #f0f2f5, #ffffff);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1), 0 5px 15px rgba(0,0,0,0.07);
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.2);
}

.profile-header {
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    padding: 40px 30px;
    text-align: center;
    position: relative;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.simple-profile h2 {
    color: white;
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

.profile-content {
    padding: 40px;
    background: white;
}

.success-msg {
    background: linear-gradient(135deg, #00b09b, #96c93d);
    color: white;
    padding: 18px 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    border: none;
    box-shadow: 0 8px 25px rgba(0, 176, 155, 0.3);
    font-weight: 500;
    font-size: 16px;
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    font-weight: 700;
    margin-bottom: 10px;
    color: #2c3e50;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input {
    width: 100%;
    padding: 18px 20px;
    border: 2px solid #e8ecf4;
    border-radius: 15px;
    font-size: 16px;
    box-sizing: border-box;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(145deg, #f0f8fa, #ffffff);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
}

.form-group input:focus {
    outline: none;
    border-color: #4a90a4;
    box-shadow: 0 0 0 4px rgba(74, 144, 164, 0.15), inset 0 2px 4px rgba(0,0,0,0.06);
    background: white;
    transform: translateY(-2px);
}

.form-group.readonly {
    opacity: 0.8;
}

.form-group.readonly input {
    background: linear-gradient(145deg, #f1f3f4, #e8eaed);
    color: #5f6368;
    cursor: not-allowed;
    border-color: #dadce0;
}

.form-group.readonly label::after {
    content: " (Chỉ xem)";
    font-size: 12px;
    color: #9aa0a6;
    font-weight: 400;
    text-transform: none;
    letter-spacing: 0;
}

.password-field {
    position: relative;
    display: flex;
}

.password-field input {
    padding-right: 60px;
}

.show-pass {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #4a90a4, #2c5f6f);
    color: white;
    border: none;
    cursor: pointer;
    padding: 10px 12px;
    font-size: 16px;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(74, 144, 164, 0.3);
}

.show-pass:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 6px 20px rgba(74, 144, 164, 0.4);
}

.form-group small {
    color: #9aa0a6;
    font-size: 14px;
    margin-top: 8px;
    display: block;
    font-style: italic;
}

.save-btn {
    background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
    color: white;
    border: none;
    padding: 20px 40px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 18px;
    font-weight: 700;
    width: 100%;
    margin-top: 30px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(74, 144, 164, 0.4);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.save-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.save-btn:hover::before {
    left: 100%;
}

.save-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(74, 144, 164, 0.6);
}

.save-btn:active {
    transform: translateY(-1px);
}

.save-btn:disabled {
    background: #9aa0a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

@@media (max-width: 768px) {
    .simple-profile {
        margin: 10px;
        padding: 15px;
    }
    
    .form-group input {
        padding: 12px;
        font-size: 16px;
    }
    
    .save-btn {
        padding: 14px;
        font-size: 16px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const toggleBtn = document.getElementById('togglePassword');
    const passwordInput = document.querySelector('input[name="MatKhau"]');
    
    if (toggleBtn && passwordInput) {
        toggleBtn.addEventListener('click', function() {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁';
            }
        });
    }

    // Load user profile
    loadProfile();

    // Form submission
    const form = document.querySelector('.profile-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            updateProfile();
        });
    }
});

async function loadProfile() {
    try {
        const response = await fetch('/api/Auth/get-user-info', {
            credentials: 'include'
        });

        if (response.ok) {
            const result = await response.json();
            if (result.data) {
                const nameInput = document.querySelector('input[name="HoTen"]');
                if (nameInput) nameInput.value = result.data.hoTen || '';
            }
        }
    } catch (error) {
        console.error('Error loading profile:', error);
    }
}

async function updateProfile() {
    const submitBtn = document.querySelector('.save-btn');
    const originalText = submitBtn.textContent;
    
    try {
        submitBtn.disabled = true;
        submitBtn.textContent = 'Đang lưu...';

        const hoTen = document.querySelector('input[name="HoTen"]').value.trim();
        const matKhau = document.querySelector('input[name="MatKhau"]').value;

        if (!hoTen) {
            showMessage('Họ tên không được để trống', 'error');
            return;
        }

        const response = await fetch('/api/Auth/profile/update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({
                HoTen: hoTen,
                MatKhau: matKhau || null
            })
        });

        const result = await response.json();

        if (response.ok) {
            showMessage(result.message || 'Cập nhật thành công!', 'success');
            document.querySelector('input[name="MatKhau"]').value = '';
        } else {
            showMessage(result.message || 'Có lỗi xảy ra', 'error');
        }

    } catch (error) {
        showMessage('Lỗi kết nối: ' + error.message, 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
}

function showMessage(message, type) {
    // Remove existing messages
    const existingMsg = document.querySelector('.success-msg, .error-msg');
    if (existingMsg) existingMsg.remove();

    // Create new message
    const msgDiv = document.createElement('div');
    msgDiv.className = type === 'success' ? 'success-msg' : 'error-msg';
    msgDiv.textContent = message;

    if (type === 'error') {
        msgDiv.style.background = '#f8d7da';
        msgDiv.style.color = '#721c24';
        msgDiv.style.border = '1px solid #f5c6cb';
    }

    // Insert message
    const form = document.querySelector('.profile-form');
    form.parentNode.insertBefore(msgDiv, form);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (msgDiv.parentNode) {
            msgDiv.remove();
        }
    }, 5000);
}
</script>
