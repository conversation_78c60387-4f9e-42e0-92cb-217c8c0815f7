@{
    ViewData["Title"] = "Dashboard Khách Hàng";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
<style>
    .stat-card.info .stat-icon i {
        color: white;
    }
    .stat-trend i {
        color: #1e293b !important;
        opacity: 1 !important;
    }
    .stat-trend span {
        color: #1e293b !important;
        opacity: 1 !important;
        display: inline !important;
    }
</style>

<div class="dashboard-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title-wrapper">
            <h1 class="page-title">
                <i class="fas fa-user-chart"></i>
                Dashboard Khách Hàng
            </h1>
            <p class="page-subtitle">Thống kê cá nhân và thông tin thuê trọ của bạn</p>
        </div>
        <div class="page-actions">
            <button class="btn btn-primary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i>
                Làm mới
            </button>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="loading-container">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Đang tải dữ liệu...</p>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div id="dashboardContent" class="dashboard-content" style="display: none;">
        
        <!-- Quick Stats Cards -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <div class="stat-content">
                    <h3 id="thangNay">0</h3>
                    <p>Tiền Phòng Tháng Này</p>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span id="tyLeThangNay">0%</span>
                    </div>
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="stat-content">
                    <h3 id="dienThangNay">0</h3>
                    <p>Tiền Điện Tháng Này</p>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span id="tyLeDien">0%</span>
                    </div>
                </div>
            </div>

            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-tint"></i>
                </div>
                <div class="stat-content">
                    <h3 id="nuocThangNay">0</h3>
                    <p>Tiền Nước Tháng Này</p>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span id="tyLeNuoc">0%</span>
                    </div>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-motorcycle"></i>
                </div>
                <div class="stat-content">
                    <h3 id="xeThangNay">0</h3>
                    <p>Tiền Xe Tháng Này</p>
                    <div class="stat-trend">
                        <i class="fas fa-motorcycle"></i>
                        <span id="soXe">0 xe</span>
                    </div>
                </div>
            </div>
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-user-friends"></i>
                </div>
                <div class="stat-content">
                    <h3 id="soNguoi">0</h3>
                    <p>Số Người Ở</p>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span id="phiDV">0 VNĐ</span>
                    </div>
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="stat-content">
                    <h3 id="namNay">0</h3>
                    <p>Tổng Chi Phí Năm Nay</p>
                    <div class="stat-trend">
                        <i class="fas fa-chart-line"></i>
                        <span id="tyLeNamNay">0%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="chart-row">
                <!-- Revenue Overview -->
                <div class="chart-card large">
                    <div class="chart-header">
                        <h3>
                            <i class="fas fa-chart-area"></i>
                            Chi Phí Theo Thời Gian
                        </h3>
                        <div class="chart-controls">
                            <select id="revenueTimeframe" class="form-control">
                                <option value="month" selected>So Sánh Tháng</option>
                                <option value="year">So Sánh Năm</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-body">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>

                <!-- Utility Bills -->
                <div class="chart-card medium">
                    <div class="chart-header">
                        <h3>
                            <i class="fas fa-chart-pie"></i>
                            Phân Bổ Chi Phí
                        </h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="utilityChart"></canvas>
                        <div class="utility-stats">
                            <div class="utility-item">
                                <span class="color-indicator primary"></span>
                                <span>Tiền phòng: <strong id="tiLePhong">0%</strong></span>
                            </div>
                            <div class="utility-item">
                                <span class="color-indicator success"></span>
                                <span>Tiền điện: <strong id="tiLeDienChart">0%</strong></span>
                            </div>
                            <div class="utility-item">
                                <span class="color-indicator info"></span>
                                <span>Tiền nước: <strong id="tiLeNuocChart">0%</strong></span>
                            </div>
                            <div class="utility-item">
                                <span class="color-indicator warning"></span>
                                <span>Tiền xe: <strong id="tiLeXeChart">0%</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comparison Charts -->
            <div class="chart-row">
                <div class="chart-card medium">
                    <div class="chart-header">
                        <h3>
                            <i class="fas fa-chart-bar"></i>
                            So Sánh Tháng Trước - Tháng Này
                        </h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="comparisonChart"></canvas>
                    </div>
                </div>

                <!-- Year Comparison -->
                <div class="chart-card medium">
                    <div class="chart-header">
                        <h3>
                            <i class="fas fa-chart-line"></i>
                            So Sánh Năm Trước - Năm Này
                        </h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="yearComparisonChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Personal Info Section -->
        <div class="info-section">
            <div class="info-card">
                <div class="info-header">
                    <h3>
                        <i class="fas fa-info-circle"></i>
                        Thông Tin Cá Nhân
                    </h3>
                </div>
                <div class="info-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="info-content">
                                <h4 id="userName">Đang tải...</h4>
                                <p>Họ và tên</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="info-content">
                                <h4 id="userPhone">Đang tải...</h4>
                                <p>Số điện thoại</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="info-content">
                                <h4 id="userEmail">Đang tải...</h4>
                                <p>Email</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="info-content">
                                <h4 id="roomInfo">Đang tải...</h4>
                                <p>Phòng đang thuê</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="info-content">
                                <h4 id="roomPrice">Đang tải...</h4>
                                <p>Giá phòng</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-ruler-combined"></i>
                            </div>
                            <div class="info-content">
                                <h4 id="roomArea">Đang tải...</h4>
                                <p>Diện tích</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="info-content">
                                <h4 id="chiSoDien">Đang tải...</h4>
                                <p>Chỉ số điện</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="info-content">
                                <h4 id="chiSoNuoc">Đang tải...</h4>
                                <p>Chỉ số nước</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let dashboardData = null;
        let revenueChart = null;
        let utilityChart = null;
        let comparisonChart = null;
        let yearComparisonChart = null;

        // Load dashboard data
        async function loadDashboardData() {
            try {
                showLoading();
                const response = await fetch('/api/Auth/Dashborad');
                const result = await response.json();
                
                if (result.success) {
                    console.log(result.data);
                    dashboardData = result.data;
                    updateDashboard();
                    hideLoading();
                } else {
                    // Hiển thị thông báo lỗi cụ thể
                    if (result.message && result.message.includes('chưa có hợp đồng')) {
                        showNoContractMessage();
                    } else {
                        throw new Error(result.message || 'Lỗi khi tải dữ liệu');
                    }
                }
            } catch (error) {
                console.error('Error loading dashboard:', error);
                hideLoading();
                showError('Không thể tải dữ liệu dashboard: ' + error.message);
            }
        }

        function showNoContractMessage() {
            hideLoading();
            const dashboardContent = document.getElementById('dashboardContent');
            dashboardContent.innerHTML = `
                <div class="no-contract-message" style="text-align: center; padding: 50px 20px;">
                    <div style="font-size: 4rem; color: #cbd5e1; margin-bottom: 20px;">
                        <i class="fas fa-home"></i>
                    </div>
                    <h2 style="color: #64748b; margin-bottom: 15px;">Chưa có hợp đồng thuê trọ</h2>
                    <p style="color: #94a3b8; margin-bottom: 30px;">
                        Bạn chưa có hợp đồng thuê trọ nào. Vui lòng liên hệ chủ trọ để được hỗ trợ.
                    </p>
                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i>
                            Thử lại
                        </button>
                        <a href="/" class="btn btn-secondary">
                            <i class="fas fa-home"></i>
                            Về trang chủ
                        </a>
                    </div>
                </div>
            `;
            dashboardContent.style.display = 'block';
        }

        // Load user info
        async function loadUserInfo() {
            try {
                const response = await fetch('/api/Auth/get-user-info');
                const result = await response.json();
                
                if (result.success) {
                    const userData = result.data;
                    document.getElementById('userName').textContent = userData.hoTen || 'N/A';
                    document.getElementById('userPhone').textContent = userData.phone || 'N/A';
                    document.getElementById('userEmail').textContent = userData.email || 'N/A';
                }
            } catch (error) {
                console.error('Error loading user info:', error);
            }
        }

        // Update room info from dashboard data
        function updateRoomInfo() {
            if (dashboardData) {
                document.getElementById('roomInfo').textContent = dashboardData.roomInfo || 'Chưa có thông tin';
                document.getElementById('roomPrice').textContent = dashboardData.roomPrice ? formatCurrency(dashboardData.roomPrice) : 'Chưa có thông tin';
                document.getElementById('roomArea').textContent = dashboardData.roomArea ? `${dashboardData.roomArea} m²` : 'Chưa có thông tin';
                document.getElementById('chiSoDien').textContent = dashboardData.chisodien ? `${dashboardData.chisodien} kWh` : 'Chưa có thông tin';
                document.getElementById('chiSoNuoc').textContent = dashboardData.chisonuoc ? `${dashboardData.chisonuoc} m³` : 'Chưa có thông tin';
            }
        }

        function showLoading() {
            document.getElementById('loadingState').style.display = 'flex';
            document.getElementById('dashboardContent').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'block';
        }

        function showError(message) {
            // You can implement a toast notification here
            alert(message);
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        }

        function updateDashboard() {
            if (!dashboardData) return;

            // Update stat cards with safe number handling
            document.getElementById('thangNay').textContent = formatCurrency(dashboardData.thangNay || 0);
            document.getElementById('dienThangNay').textContent = formatCurrency(dashboardData.dienthangnay || 0);
            document.getElementById('nuocThangNay').textContent = formatCurrency(dashboardData.nuocthangnay || 0);
            document.getElementById('xeThangNay').textContent = formatCurrency(dashboardData.giaguixe || 0);
            document.getElementById('soNguoi').textContent = dashboardData.songuoi || 0;
            document.getElementById('namNay').textContent = formatCurrency(dashboardData.namNay || 0);

            // Update trend percentages with safe number handling
            document.getElementById('tyLeThangNay').textContent = (dashboardData.tyLeThayDoiThangNay || 0).toFixed(1) + '%';
            document.getElementById('tyLeDien').textContent = (dashboardData.tyLeThayDoidien || 0).toFixed(1) + '%';
            document.getElementById('tyLeNuoc').textContent = (dashboardData.tyLeThayDoinuoc || 0).toFixed(1) + '%';
            document.getElementById('tyLeNamNay').textContent = (dashboardData.tyLeThayDoiNamNay || 0).toFixed(1) + '%';

            // Update additional info
            document.getElementById('soXe').textContent = (dashboardData.soxe || 0) + ' xe';
            document.getElementById('phiDV').textContent = formatCurrency(dashboardData.phiDV || 0) + ' 1 Phòng';

            // Create charts
            createRevenueChart();
            createUtilityChart();
            createComparisonChart();
            createYearComparisonChart();
            
            // Update room info
            updateRoomInfo();
        }

        function createRevenueChart() {
            const ctx = document.getElementById('revenueChart').getContext('2d');
            
            if (revenueChart) {
                revenueChart.destroy();
            }

            const data = {
                labels: ['Tháng trước', 'Tháng này', 'Năm trước', 'Năm này'],
                datasets: [{
                    label: 'Chi phí (VNĐ)',
                    data: [
                        dashboardData.thangTruoc || 0,
                        dashboardData.thangNay || 0,
                        dashboardData.namTruoc || 0,
                        dashboardData.namNay || 0
                    ],
                    backgroundColor: 'rgba(74, 144, 164, 0.1)',
                    borderColor: '#4a90a4',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            };

            revenueChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        function createUtilityChart() {
            const ctx = document.getElementById('utilityChart').getContext('2d');
            
            if (utilityChart) {
                utilityChart.destroy();
            }

            const thangNay = dashboardData.thangNay || 0;
            const dienThangNay = dashboardData.dienthangnay || 0;
            const nuocThangNay = dashboardData.nuocthangnay || 0;
            const xeThangNay = dashboardData.giaguixe || 0;
            const totalCost = thangNay + dienThangNay + nuocThangNay + xeThangNay;
            const roomPercent = totalCost > 0 ? ((thangNay / totalCost) * 100).toFixed(1) : 0;
            const electricPercent = totalCost > 0 ? ((dienThangNay / totalCost) * 100).toFixed(1) : 0;
            const waterPercent = totalCost > 0 ? ((nuocThangNay / totalCost) * 100).toFixed(1) : 0;
            const xePercent = totalCost > 0 ? ((xeThangNay / totalCost) * 100).toFixed(1) : 0;

            document.getElementById('tiLePhong').textContent = roomPercent + '%';
            document.getElementById('tiLeDienChart').textContent = electricPercent + '%';
            document.getElementById('tiLeNuocChart').textContent = waterPercent + '%';
            document.getElementById('tiLeXeChart').textContent = xePercent + '%';

            const data = {
                labels: ['Tiền phòng', 'Tiền điện', 'Tiền nước', 'Tiền xe'],
                datasets: [{
                    data: [thangNay, dienThangNay, nuocThangNay, xeThangNay],
                    backgroundColor: ['#4a90a4', '#f59e0b', '#06b6d4', '#f97316'],
                    borderWidth: 0
                }]
            };

            utilityChart = new Chart(ctx, {
                type: 'doughnut',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function createComparisonChart() {
            const ctx = document.getElementById('comparisonChart').getContext('2d');
            
            if (comparisonChart) {
                comparisonChart.destroy();
            }

            const data = {
                labels: ['Tiền phòng', 'Tiền điện', 'Tiền nước', 'Tiền xe'],
                datasets: [
                    {
                        label: 'Tháng trước',
                        data: [dashboardData.thangTruoc || 0, dashboardData.dienthangtruoc || 0, dashboardData.nuocthangtruoc || 0, 0], // Tiền xe tháng trước chưa có
                        backgroundColor: '#e5e7eb',
                        borderRadius: 8
                    },
                    {
                        label: 'Tháng này',
                        data: [thangNay, dienThangNay, nuocThangNay, xeThangNay],
                        backgroundColor: '#4a90a4',
                        borderRadius: 8
                    }
                ]
            };

            comparisonChart = new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        function createYearComparisonChart() {
            const ctx = document.getElementById('yearComparisonChart').getContext('2d');
            
            if (yearComparisonChart) {
                yearComparisonChart.destroy();
            }

            const data = {
                labels: ['Năm trước', 'Năm này'],
                datasets: [{
                    label: 'Tổng chi phí (VNĐ)',
                    data: [dashboardData.namTruoc || 0, dashboardData.namNay || 0],
                    backgroundColor: ['#e5e7eb', '#4a90a4'],
                    borderColor: ['#9ca3af', '#4a90a4'],
                    borderWidth: 2,
                    borderRadius: 8
                }]
            };

            yearComparisonChart = new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        function refreshDashboard() {
            loadDashboardData();
            loadUserInfo();
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            loadUserInfo();
        });
    </script>
} 