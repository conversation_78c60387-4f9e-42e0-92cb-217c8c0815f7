@{
    ViewData["Title"] = "Đền bù của tôi";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

@section Styles {
    <link rel="stylesheet" href="~/css/denbu.css" />
}

<div class="container-fluid px-4">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="page-title mb-1">
                        <i class="fas fa-hand-holding-usd text-primary me-2"></i>
                        Đền bù của tôi
                    </h1>
                    <p class="text-muted mb-0">Xem danh sách các khoản đền bù liên quan đến hợp đồng thuê trọ</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/Users/<USER>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Đ<PERSON><PERSON> bù</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Thống kê tổng quan -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Tổng số đền bù
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalCompensations">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Tổng tiền đền bù
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalAmount">0 VNĐ</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Đền bù tháng này
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="thisMonthCount">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Đền bù gần nhất
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="latestDate">Chưa có</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bộ lọc và tìm kiếm -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Bộ lọc
                    </h5>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-outline-secondary btn-sm" onclick="resetFilters()">
                        <i class="fas fa-undo me-1"></i>
                        Đặt lại
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Tìm kiếm theo nội dung</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Nhập từ khóa...">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Sắp xếp theo</label>
                    <select class="form-select" id="sortSelect">
                        <option value="date-desc">Ngày tạo (Mới nhất)</option>
                        <option value="date-asc">Ngày tạo (Cũ nhất)</option>
                        <option value="amount-desc">Số tiền (Cao nhất)</option>
                        <option value="amount-asc">Số tiền (Thấp nhất)</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Lọc theo nhà trọ</label>
                    <select class="form-select" id="motelFilter">
                        <option value="">Tất cả nhà trọ</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="applyFilters()">
                        <i class="fas fa-search me-1"></i>
                        Lọc
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Danh sách đền bù -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Danh sách đền bù
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="compensationTable">
                    <thead class="table-light">
                        <tr>
                            <th>Mã đền bù</th>
                            <th>Nhà trọ</th>
                            <th>Nội dung</th>
                            <th>Số tiền</th>
                            <th>Ngày tạo</th>
                            <th>Hình ảnh</th>
                        </tr>
                    </thead>
                    <tbody id="compensationTableBody">
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Đang tải...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Phân trang -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    Hiển thị <span id="showingFrom">0</span> - <span id="showingTo">0</span> trong tổng số <span id="totalItems">0</span> bản ghi
                </div>
                <nav aria-label="Phân trang">
                    <ul class="pagination pagination-sm mb-0" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Modal chi tiết đền bù -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Chi tiết đền bù
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailModalBody">
                <!-- Nội dung sẽ được load động -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/denbu-user.js?v=@DateTime.Now"></script>
} 