@{
    ViewData["Title"] = "Quên mật khẩu";
}
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>uên mật khẩu - Nhà trọ</title>
    <style>
        .recover-page {
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .recover-container {
            background: #fff;
            padding: 40px 30px;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(49,130,206,0.10), 0 1.5px 6px rgba(99,179,237,0.08);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        .recover-container .icon {
            background: linear-gradient(135deg, #3182ce 0%, #63b3ed 100%);
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 30px;
            color: #fff;
            box-shadow: 0 2px 8px rgba(49,130,206,0.10);
        }
        .recover-container h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 8px;
            font-weight: 700;
        }
        .recover-container .subtitle {
            color: #6c757d;
            margin-bottom: 24px;
            font-size: 15px;
            line-height: 1.5;
        }
        .recover-container .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        .recover-container .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #495057;
            font-weight: 500;
            font-size: 14px;
        }
        .recover-container .form-input {
            width: 100%;
            padding: 13px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            color: #495057;
            transition: all 0.3s;
            background: #f8f9fa;
            box-sizing: border-box;
        }
        .recover-container .form-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px #3182ce33;
            background: #fff;
        }
        .recover-container .code-input-container {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 18px 0 24px;
        }
        .recover-container .code-input {
            width: 48px;
            height: 48px;
            border: 2px solid #63b3ed;
            border-radius: 10px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: #3182ce;
            transition: all 0.3s;
            font-family: monospace;
            background: #f8f9fa;
        }
        .recover-container .code-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px #3182ce33;
        }
        .recover-container .btn {
            width: 100%;
            padding: 13px;
            background: linear-gradient(135deg, #3182ce 0%, #63b3ed 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s, box-shadow 0.2s;
            margin-bottom: 18px;
            box-shadow: 0 2px 8px rgba(49,130,206,0.08);
        }
        .recover-container .btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #2563eb 0%, #4299e1 100%);
        }
        .recover-container .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .recover-container .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 16px;
            border: 1px solid #c3e6cb;
            display: none;
            font-size: 15px;
        }
        .recover-container .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 16px;
            border: 1px solid #f5c6cb;
            display: none;
            font-size: 15px;
        }
        .recover-container .timer {
            color: #dc3545;
            font-weight: 600;
            font-size: 14px;
            margin-top: 8px;
        }
        @@media (max-width: 480px) {
            .recover-container {
                padding: 20px 8px;
                margin: 10px;
            }
            .recover-container .code-input {
                width: 38px;
                height: 38px;
                font-size: 16px;
            }
            .recover-container .code-input-container {
                gap: 6px;
            }
        }
    </style>
</head>
<body>
    <div class="recover-page">
        <div class="recover-container">
            <div class="icon">🔒</div>
            <h1>Quên mật khẩu</h1>
            <p class="subtitle">
                Nhập email đăng ký để nhận mã xác nhận đặt lại mật khẩu.
            </p>
            <div class="success-message" id="successMessage"></div>
            <div class="error-message" id="errorMessage"></div>
            <form id="recoverForm">
                <div id="emailSection">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" class="form-input" placeholder="Nhập email đăng ký" required>
                    </div>
                    <button type="submit" class="btn" id="sendCodeBtn">Gửi mã xác nhận</button>
                </div>
                <div id="codeSection" style="display:none;">
                    <div class="code-input-container">
                        <input type="text" class="code-input" maxlength="1" data-index="0">
                        <input type="text" class="code-input" maxlength="1" data-index="1">
                        <input type="text" class="code-input" maxlength="1" data-index="2">
                        <input type="text" class="code-input" maxlength="1" data-index="3">
                        <input type="text" class="code-input" maxlength="1" data-index="4">
                        <input type="text" class="code-input" maxlength="1" data-index="5">
                    </div>
                    <button type="button" class="btn" id="verifyCodeBtn">Xác thực mã</button>
                    <div class="timer" id="timer" style="display:none;">Gửi lại mã sau: <span id="countdown">180</span>s</div>
                    <button type="button" class="btn" id="resendBtn" style="display:none; margin-top:8px;">Gửi lại mã</button>
                </div>
                <div id="passwordSection" style="display:none;">
                    <div class="form-group">
                        <label for="newPassword">Mật khẩu mới</label>
                        <input type="password" id="newPassword" class="form-input" placeholder="Nhập mật khẩu mới" required>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">Xác nhận mật khẩu mới</label>
                        <input type="password" id="confirmPassword" class="form-input" placeholder="Nhập lại mật khẩu mới" required>
                    </div>
                    <button type="submit" class="btn" id="resetBtn">Đổi mật khẩu</button>
                </div>
            </form>
        </div>
    </div>
    <script>
        const recoverForm = document.getElementById('recoverForm');
        const emailSection = document.getElementById('emailSection');
        const codeSection = document.getElementById('codeSection');
        const passwordSection = document.getElementById('passwordSection');
        const sendCodeBtn = document.getElementById('sendCodeBtn');
        const verifyCodeBtn = document.getElementById('verifyCodeBtn');
        const resetBtn = document.getElementById('resetBtn');
        const resendBtn = document.getElementById('resendBtn');
        const timer = document.getElementById('timer');
        const countdown = document.getElementById('countdown');
        const successMessage = document.getElementById('successMessage');
        const errorMessage = document.getElementById('errorMessage');
        const codeInputs = document.querySelectorAll('.code-input');
        const newPassword = document.getElementById('newPassword');
        const confirmPassword = document.getElementById('confirmPassword');
        let email = '';
        let timeLeft = 180;
        let timerInterval;
        // Bước 1: Gửi mã xác nhận
        sendCodeBtn.addEventListener('click', async function(e) {
            e.preventDefault();
            email = document.getElementById('email').value;
            if (!email) return showError('Vui lòng nhập email.');
            sendCodeBtn.disabled = true;
            try {
                const res = await fetch('/api/auth/forgot-password', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email })
                });
                const result = await res.json();
                if (res.ok) {
                    showSuccess(result.message || 'Mã xác nhận đã được gửi đến email của bạn!');
                    emailSection.style.display = 'none';
                    codeSection.style.display = '';
                    passwordSection.style.display = 'none';
                    sessionStorage.setItem('resetEmail', email);
                    startTimer();
                    codeInputs[0].focus();
                } else {
                    showError(result.message || 'Không tìm thấy tài khoản với email này.');
                }
            } catch (err) {
                showError('Có lỗi xảy ra khi gửi mã xác nhận.');
            }
            sendCodeBtn.disabled = false;
        });
        // Bước 2: Xác thực mã code
        verifyCodeBtn.addEventListener('click', async function(e) {
            e.preventDefault();
            const code = Array.from(codeInputs).map(input => input.value).join('');
            if (code.length !== 6) return showError('Vui lòng nhập đủ 6 số mã xác nhận.');
            verifyCodeBtn.disabled = true;
            try {
                // Gọi API xác thực code (dùng verify-reset-code với mật khẩu tạm)
                const res = await fetch('/api/auth/verify-reset-code', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: email, code: code, newPassword: '___dummy___' })
                });
                const result = await res.json();
                if (res.ok || (result.message && result.message.includes('Đổi mật khẩu thành công'))) {
                    showSuccess('Mã xác nhận đúng! Vui lòng nhập mật khẩu mới.');
                    codeSection.style.display = 'none';
                    passwordSection.style.display = '';
                } else if (result.message && result.message.includes('Mã xác nhận đã hết hạn')) {
                    showError(result.message);
                } else {
                    showError(result.message || 'Mã xác nhận không đúng hoặc đã hết hạn.');
                }
            } catch (err) {
                showError('Có lỗi xảy ra khi xác thực mã.');
            }
            verifyCodeBtn.disabled = false;
        });
        // Bước 3: Đổi mật khẩu
        resetBtn.addEventListener('click', async function(e) {
            e.preventDefault();
            const code = Array.from(codeInputs).map(input => input.value).join('');
            if (!newPassword.value || newPassword.value.length < 8) return showError('Mật khẩu mới phải có ít nhất 8 ký tự.');
            if (newPassword.value !== confirmPassword.value) return showError('Mật khẩu xác nhận không khớp.');
            resetBtn.disabled = true;
            try {
                const res = await fetch('/api/auth/verify-reset-code', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: email, code: code, newPassword: newPassword.value })
                });
                const result = await res.json();
                if (res.ok) {
                    showSuccess('Đổi mật khẩu thành công! Chuyển về đăng nhập...');
                    setTimeout(() => { window.location.href = '/Users/<USER>'; }, 2000);
                } else {
                    showError(result.message || 'Có lỗi khi đổi mật khẩu.');
                }
            } catch (err) {
                showError('Có lỗi xảy ra khi đổi mật khẩu.');
            }
            resetBtn.disabled = false;
        });
        // Xử lý nhập mã code
        codeInputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                const value = e.target.value;
                if (!/^[0-9]*$/.test(value)) {
                    e.target.value = '';
                    return;
                }
                if (value && index < codeInputs.length - 1) {
                    codeInputs[index + 1].focus();
                }
            });
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    codeInputs[index - 1].focus();
                }
            });
            input.addEventListener('paste', (e) => {
                e.preventDefault();
                const pastedData = e.clipboardData.getData('text').replace(/\D/g, '');
                for (let i = 0; i < Math.min(pastedData.length, codeInputs.length - index); i++) {
                    if (codeInputs[index + i]) {
                        codeInputs[index + i].value = pastedData[i];
                    }
                }
            });
        });
        // Gửi lại mã
        resendBtn.addEventListener('click', async () => {
            resendBtn.disabled = true;
            try {
                const res = await fetch('/api/auth/forgot-password', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email })
                });
                const result = await res.json();
                if (res.ok) {
                    showSuccess(result.message || 'Mã xác nhận mới đã được gửi đến email của bạn!');
                    startTimer();
                    codeInputs.forEach(input => input.value = '');
                    codeInputs[0].focus();
                } else {
                    showError(result.message || 'Không gửi lại được mã xác nhận.');
                }
            } catch (err) {
                showError('Có lỗi xảy ra khi gửi lại mã xác nhận.');
            }
            resendBtn.disabled = false;
        });
        function startTimer() {
            timeLeft = 180;
            resendBtn.style.display = 'none';
            timer.style.display = 'block';
            countdown.textContent = timeLeft;
            if (timerInterval) clearInterval(timerInterval);
            timerInterval = setInterval(() => {
                timeLeft--;
                countdown.textContent = timeLeft;
                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    resendBtn.style.display = 'block';
                    timer.style.display = 'none';
                }
            }, 1000);
        }
        function showSuccess(msg) {
            successMessage.textContent = msg;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }
        function showError(msg) {
            errorMessage.textContent = msg;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }
        // Auto focus vào ô đầu tiên khi trang load
        window.addEventListener('load', () => {
            document.getElementById('email').focus();
        });
    </script>
</body>
</html> 