@{
    ViewData["Title"] = "Đặt lại mật khẩu";
}
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đặt lại mật khẩu - Nhà trọ</title>
    <style>
        .reset-page {
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .reset-container {
            background: #fff;
            padding: 40px 30px;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(49,130,206,0.10), 0 1.5px 6px rgba(99,179,237,0.08);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        .reset-container .icon {
            background: linear-gradient(135deg, #3182ce 0%, #63b3ed 100%);
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 30px;
            color: #fff;
            box-shadow: 0 2px 8px rgba(49,130,206,0.10);
        }
        .reset-container h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 8px;
            font-weight: 700;
        }
        .reset-container .subtitle {
            color: #6c757d;
            margin-bottom: 24px;
            font-size: 15px;
            line-height: 1.5;
        }
        .reset-container .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        .reset-container .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #495057;
            font-weight: 500;
            font-size: 14px;
        }
        .reset-container .form-input {
            width: 100%;
            padding: 13px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            color: #495057;
            transition: all 0.3s;
            background: #f8f9fa;
            box-sizing: border-box;
        }
        .reset-container .form-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px #3182ce33;
            background: #fff;
        }
        .reset-container .code-input-container {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 18px 0 24px;
        }
        .reset-container .code-input {
            width: 48px;
            height: 48px;
            border: 2px solid #63b3ed;
            border-radius: 10px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: #3182ce;
            transition: all 0.3s;
            font-family: monospace;
            background: #f8f9fa;
        }
        .reset-container .code-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 2px #3182ce33;
        }
        .reset-container .btn {
            width: 100%;
            padding: 13px;
            background: linear-gradient(135deg, #3182ce 0%, #63b3ed 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s, box-shadow 0.2s;
            margin-bottom: 18px;
            box-shadow: 0 2px 8px rgba(49,130,206,0.08);
        }
        .reset-container .btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #2563eb 0%, #4299e1 100%);
        }
        .reset-container .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .reset-container .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 16px;
            border: 1px solid #c3e6cb;
            display: none;
            font-size: 15px;
        }
        .reset-container .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 16px;
            border: 1px solid #f5c6cb;
            display: none;
            font-size: 15px;
        }
        @@media (max-width: 480px) {
            .reset-container {
                padding: 20px 8px;
                margin: 10px;
            }
            .reset-container .code-input {
                width: 38px;
                height: 38px;
                font-size: 16px;
            }
            .reset-container .code-input-container {
                gap: 6px;
            }
        }
    </style>
</head>
<body>
    <div class="reset-page">
        <div class="reset-container">
            <div class="icon">🔑</div>
            <h1>Đặt lại mật khẩu</h1>
            <p class="subtitle">
                Nhập mã xác nhận đã gửi về email và đặt lại mật khẩu mới.
            </p>
            <div class="success-message" id="successMessage">
                ✅ Đổi mật khẩu thành công! Chuyển hướng về đăng nhập...
            </div>
            <div class="error-message" id="errorMessage">
                ❌ Mã xác nhận không đúng hoặc có lỗi xảy ra.
            </div>
            <form id="resetForm">
                <div class="code-input-container">
                    <input type="text" class="code-input" maxlength="1" data-index="0">
                    <input type="text" class="code-input" maxlength="1" data-index="1">
                    <input type="text" class="code-input" maxlength="1" data-index="2">
                    <input type="text" class="code-input" maxlength="1" data-index="3">
                    <input type="text" class="code-input" maxlength="1" data-index="4">
                    <input type="text" class="code-input" maxlength="1" data-index="5">
                </div>
                <div class="form-group">
                    <label for="newPassword">Mật khẩu mới</label>
                    <input type="password" id="newPassword" class="form-input" placeholder="Nhập mật khẩu mới" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Xác nhận mật khẩu mới</label>
                    <input type="password" id="confirmPassword" class="form-input" placeholder="Nhập lại mật khẩu mới" required>
                </div>
                <button type="submit" class="btn" id="resetBtn">Đổi mật khẩu</button>
            </form>
        </div>
    </div>
    <script>
        const codeInputs = document.querySelectorAll('.code-input');
        const resetBtn = document.getElementById('resetBtn');
        const successMessage = document.getElementById('successMessage');
        const errorMessage = document.getElementById('errorMessage');
        const newPassword = document.getElementById('newPassword');
        const confirmPassword = document.getElementById('confirmPassword');
        // Lấy email từ sessionStorage
        const email = sessionStorage.getItem('resetEmail');
        // Xử lý nhập mã code
        codeInputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                const value = e.target.value;
                if (!/^[0-9]*$/.test(value)) {
                    e.target.value = '';
                    return;
                }
                if (value && index < codeInputs.length - 1) {
                    codeInputs[index + 1].focus();
                }
            });
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    codeInputs[index - 1].focus();
                }
            });
            input.addEventListener('paste', (e) => {
                e.preventDefault();
                const pastedData = e.clipboardData.getData('text').replace(/\D/g, '');
                for (let i = 0; i < Math.min(pastedData.length, codeInputs.length - index); i++) {
                    if (codeInputs[index + i]) {
                        codeInputs[index + i].value = pastedData[i];
                    }
                }
            });
        });
        // Xử lý submit form
        document.getElementById('resetForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const code = Array.from(codeInputs).map(input => input.value).join('');
            if (code.length !== 6) {
                showError('Vui lòng nhập đủ 6 số mã xác nhận.');
                return;
            }
            if (!newPassword.value || newPassword.value.length < 8) {
                showError('Mật khẩu mới phải có ít nhất 8 ký tự.');
                return;
            }
            if (newPassword.value !== confirmPassword.value) {
                showError('Mật khẩu xác nhận không khớp.');
                return;
            }
            resetBtn.textContent = 'Đang xử lý...';
            resetBtn.disabled = true;
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            try {
                const res = await fetch('/api/auth/verify-reset-code', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: email, code: code, newPassword: newPassword.value })
                });
                const result = await res.json();
                if (res.ok) {
                    successMessage.style.display = 'block';
                    errorMessage.style.display = 'none';
                    setTimeout(() => {
                        window.location.href = '/Users/<USER>';
                    }, 2000);
                } else {
                    errorMessage.textContent = result.message || 'Mã xác nhận không đúng hoặc có lỗi xảy ra.';
                    errorMessage.style.display = 'block';
                    successMessage.style.display = 'none';
                }
            } catch (err) {
                errorMessage.textContent = 'Có lỗi xảy ra khi đổi mật khẩu.';
                errorMessage.style.display = 'block';
                successMessage.style.display = 'none';
            }
            resetBtn.textContent = 'Đổi mật khẩu';
            resetBtn.disabled = false;
        });
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }
        // Auto focus vào ô đầu tiên khi trang load
        window.addEventListener('load', () => {
            codeInputs[0].focus();
        });
    </script>
</body>
</html> 