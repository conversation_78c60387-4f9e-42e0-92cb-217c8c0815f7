@{
    ViewData["Title"] = "Quản lý Hợ<PERSON>ồ<PERSON>";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="contract-management">
    <div class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1><i class="fas fa-handshake"></i> <PERSON>uản lý <PERSON>ợp <PERSON></h1>
                <p><PERSON>h<PERSON><PERSON>, sử<PERSON>, xóa và quản lý các hợp đồng thuê trọ với thông tin chi tiết</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" id="addContractBtn">
                    <i class="fas fa-plus"></i> Thêm hợp đồng
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <div class="filters-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Tì<PERSON> kiếm theo ID hợp đồng, ph<PERSON>ng, nhà trọ, loại phòng...">
            </div>
            
            <div class="filter-group">
                <select id="roomFilter">
                    <option value="">Tất cả phòng</option>
                </select>
                
                <select id="statusFilter">
                    <option value="">Tất cả trạng thái</option>
                    <option value="false">Đang hiệu lực</option>
                    <option value="true">Đã kết thúc</option>
                </select>
                
                <button class="btn btn-secondary" id="clearFiltersBtn">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Contracts Table -->
    <div class="table-section">
        <div class="table-container">
            <div class="table-responsive">
                <table class="contracts-table">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Phòng & Nhà trọ</th>
                            <th>Ngày bắt đầu</th>
                            <th>Ngày kết thúc</th>
                            <th>Người & Xe</th>
                            <th>Tiền đặt cọc</th>
                            <th>Khách thuê</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="contractsTableBody">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Cards -->
        <div class="mobile-cards" id="mobileContractCards">
            <!-- Mobile cards will be loaded here -->
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Hiển thị 0 - 0 của 0 hợp đồng</span>
                <div class="items-per-page">
                    <label for="itemsPerPage">Hiển thị:</label>
                    <select id="itemsPerPage">
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                    <span>hợp đồng/trang</span>
                </div>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-outline" id="prevPageBtn" disabled>
                    <i class="fas fa-chevron-left"></i> Trước
                </button>
                <div class="page-numbers" id="pageNumbers"></div>
                <button class="btn btn-outline" id="nextPageBtn" disabled>
                    Sau <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Contract Modal -->
<div class="modal" id="contractModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">
                <i class="fas fa-handshake"></i> 
                <span id="modalTitleText">Thêm hợp đồng</span>
            </h2>
            <button class="modal-close" id="closeModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="contractForm" class="modal-body">
            <div class="form-row">
                <div class="form-group">
                    <label for="roomId">Phòng <span class="required">*</span></label>
                    <select id="roomId" name="roomId" required>
                        <option value="">Chọn phòng</option>
                    </select>
                    <div class="error-message" id="roomIdError"></div>
                </div>
                
                <div class="form-group">
                    <label for="numberOfTenants">Số người ở <span class="required">*</span></label>
                    <input type="number" id="numberOfTenants" name="numberOfTenants" required min="1" max="10"
                           placeholder="Nhập số người ở">
                    <div class="error-message" id="numberOfTenantsError"></div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="startDate">Ngày bắt đầu <span class="required">*</span></label>
                    <input type="date" id="startDate" name="startDate" required>
                    <div class="error-message" id="startDateError"></div>
                </div>
                
                <div class="form-group">
                    <label for="endDate">Ngày kết thúc <span class="required">*</span></label>
                    <input type="date" id="endDate" name="endDate" required>
                    <div class="error-message" id="endDateError"></div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="soxe">Số xe</label>
                    <input type="number" id="soxe" name="soxe" min="0" max="10"
                           placeholder="Nhập số xe (tùy chọn)">
                    <div class="error-message" id="soxeError"></div>
                </div>
                
                <div class="form-group">
                    <label for="depositAmount">Tiền đặt cọc (VNĐ) <span class="required">*</span></label>
                    <input type="number" id="depositAmount" name="depositAmount" required min="0" step="1000"
                           placeholder="Nhập tiền đặt cọc">
                    <div class="error-message" id="depositAmountError"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="tenantIds">Khách thuê <span class="required">*</span></label>
                <select id="tenantIds" name="tenantIds" multiple required>
                    <!-- Tenants will be loaded here -->
                </select>
                <div class="file-info">
                    <small>Giữ Ctrl (hoặc Cmd) để chọn nhiều khách thuê</small>
                    <small id="tenantSelectionInfo" style="color: #0066cc; display: block; margin-top: 5px;">
                        📋 Khi thêm hợp đồng mới: chỉ hiển thị khách hàng chưa thuê phòng
                    </small>
                </div>
                <div class="error-message" id="tenantIdsError"></div>
            </div>
            
            <div class="form-group" id="statusGroup" style="display: none;">
                <label for="isCompleted">Trạng thái hợp đồng</label>
                <select id="isCompleted" name="isCompleted">
                    <option value="false">Đang hiệu lực</option>
                    <option value="true">Đã kết thúc</option>
                </select>
                <div class="error-message" id="isCompletedError"></div>
            </div>
        </form>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-primary" id="saveContractBtn">
                <i class="fas fa-save"></i> <span id="saveBtnText">Lưu</span>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteModal">
    <div class="modal-content delete-modal">
        <div class="modal-header">
            <h2><i class="fas fa-exclamation-triangle text-danger"></i> Xác nhận xóa</h2>
            <button class="modal-close" id="closeDeleteModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <p>Bạn có chắc chắn muốn xóa hợp đồng <strong id="deleteContractId"></strong> không?</p>
            <p class="text-danger"><i class="fas fa-warning"></i> Hành động này sẽ xóa tất cả dữ liệu liên quan (hóa đơn, lịch sử thanh toán, đền bù) và không thể hoàn tác!</p>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i> Xóa
            </button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Đang tải dữ liệu...</p>
    </div>
</div>

<style>
    /* Enhanced table for better information display */
    .contracts-table th:nth-child(2),
    .contracts-table td:nth-child(2) {
        min-width: 200px;
    }
    
    .contracts-table th:nth-child(7),
    .contracts-table td:nth-child(7) {
        min-width: 150px;
    }
    
    .tenant-info {
        font-size: 12px;
        color: #6b7280;
        margin-top: 2px;
    }
    
    .room-info {
        display: flex;
        flex-direction: column;
    }
    
    .room-main {
        font-weight: 600;
        color: #1f2937;
    }
    
    .room-details {
        font-size: 12px;
        color: #6b7280;
        margin-top: 2px;
    }
    
    .text-muted {
        color: #6b7280 !important;
        font-size: 0.875rem;
    }
    
    .people-vehicles {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    
    .people-vehicles span {
        font-size: 13px;
    }
    
    .tenant-list {
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        color: #4a90a4;
        cursor: help;
    }
    
    .tenant-list:hover {
        overflow: visible;
        white-space: normal;
        background: #f8fafc;
        padding: 4px 8px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        position: relative;
        z-index: 10;
    }

    /* Ensure modal is properly displayed */
    .modal {
        z-index: 99999 !important;
        align-items: flex-start !important;
        justify-content: center !important;
        padding: 20px !important;
        padding-top: 10px !important;
        padding-bottom: 60px !important;
        overflow-y: auto !important;
    }
    
    .modal-content {
        max-height: 80vh !important;
        overflow: visible !important;
        margin: 0 auto 50px auto !important;
        position: relative !important;
        top: 0 !important;
        display: flex !important;
        flex-direction: column !important;
    }
    
    .modal-body {
        max-height: calc(80vh - 160px) !important;
        overflow-y: auto !important;
        flex: 1 !important;
    }
    
    .modal-footer {
        position: sticky !important;
        bottom: 0 !important;
        background: #f8fafc !important;
        border-top: 1px solid #e5e7eb !important;
        z-index: 100 !important;
        flex-shrink: 0 !important;
        margin-top: auto !important;
    }
    
    /* Desktop specific */
    @@media (min-width: 769px) {
        .modal {
            align-items: flex-start !important;
            justify-content: center !important;
            padding: 30px !important;
            padding-top: 20px !important;
            padding-bottom: 80px !important;
        }
        
        .modal-content {
            width: 700px !important;
            max-width: 90vw !important;
            max-height: 75vh !important;
            margin: 0 auto 50px auto !important;
        }
        
        .modal-body {
            max-height: calc(75vh - 160px) !important;
        }
    }
    
    /* Mobile specific fixes */
    @@media (max-width: 768px) {
        .modal {
            align-items: flex-start !important;
            justify-content: center !important;
            padding: 15px !important;
            padding-top: 10px !important;
            padding-bottom: 50px !important;
        }
        
        .modal-content {
            margin: 0 auto !important;
            width: 100% !important;
            max-width: none !important;
            max-height: 82vh !important;
        }
        
        .modal-body {
            max-height: calc(82vh - 160px) !important;
        }
        
        .modal-footer {
            flex-direction: column !important;
            gap: 10px !important;
        }
        
        .modal-footer .btn {
            width: 100% !important;
            min-height: 50px !important;
            font-size: 16px !important;
        }
    }
    
    @@media (max-width: 480px) {
        .modal {
            padding: 10px !important;
            padding-top: 5px !important;
            padding-bottom: 40px !important;
        }
        
        .modal-content {
            max-height: 85vh !important;
        }
        
        .modal-body {
            max-height: calc(85vh - 140px) !important;
        }
        
        .modal-footer .btn {
            min-height: 52px !important;
            font-weight: 700 !important;
        }
    }
    
    /* Form row layout */
    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-row .form-group {
        flex: 1;
        margin-bottom: 0;
    }
    
    @@media (max-width: 768px) {
        .form-row {
            flex-direction: column;
            gap: 0;
        }
        
        .form-row .form-group {
            margin-bottom: 20px;
        }
    }
    
    /* Multi-select styling */
    select[multiple] {
        height: 120px !important;
        resize: vertical;
    }
    
    select[multiple] option {
        padding: 8px 12px;
        margin: 2px 0;
    }
    
    select[multiple] option:checked {
        background: #4a90a4 !important;
        color: white !important;
    }
    
    /* Pagination styles */
    .pagination-info {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }
    
    .items-per-page {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
    }
    
    .items-per-page label {
        margin: 0;
        font-weight: 500;
        color: #6b7280;
    }
    
    .items-per-page select {
        padding: 4px 8px;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        background: white;
        font-size: 14px;
        min-width: 60px;
    }
    
    .items-per-page select:focus {
        outline: none;
        border-color: #4a90a4;
        box-shadow: 0 0 0 2px rgba(74, 144, 164, 0.1);
    }
    
    .items-per-page span {
        color: #6b7280;
    }
    
    @@media (max-width: 768px) {
        .pagination-info {
            justify-content: center;
            gap: 15px;
        }
        
        .items-per-page {
            font-size: 13px;
        }
    }
</style>

<link href="~/css/contract-management.css?@DateTime.Now" rel="stylesheet">
<script src="~/js/contract-management.js?@DateTime.Now"></script> 