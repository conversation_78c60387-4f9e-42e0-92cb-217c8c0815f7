﻿@{
    ViewData["Title"] = "Về Chúng Tôi - Dị<PERSON> Vụ <PERSON> Thuê Phòng Trọ Chất L<PERSON>ợ<PERSON>";
    ViewData["Description"] = "Khám phá câu chuyện về chúng tôi - đơn vị tiên phong trong dịch vụ cho thuê phòng trọ tại Việt Nam. Tìm hiểu sứ mệnh, tầm nhìn và cam kết mang đến trải nghiệm thuê phòng tốt nhất.";
    ViewData["Keywords"] = "về chúng tôi, cho thuê phòng trọ, dịch vụ cho thuê, phòng trọ chất lư<PERSON>, thuê phòng Việt Nam, nhà trọ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Hero Section -->
<section class="hero-about py-5" style="background: var(--primary-gradient); color: white; margin-top: -5px; padding-top: 120px;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 fade-in-up">
                <h1 class="display-4 fw-bold mb-4">Về Chúng Tôi</h1>
                <p class="lead mb-4">
                    Chúng tôi là đơn vị tiên phong trong lĩnh vực cung cấp dịch vụ cho thuê phòng trọ chất lượng cao,
                    mang đến giải pháp an cư lý tưởng cho sinh viên và người lao động trẻ.
                </p>
                <div class="d-flex gap-3">
                    <a href="#story" class="btn btn-accent">Câu Chuyện Của Chúng Tôi</a>
                    <a href="/Contact" class="btn btn-outline-light">Liên Hệ Ngay</a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <img src="/images/about-hero.jpg" alt="Về chúng tôi - Dịch vụ cho thuê phòng trọ"
                     class="img-fluid rounded shadow-lg" style="max-height: 400px; object-fit: cover;">
            </div>
        </div>
    </div>
</section>

<!-- Story Section -->
<section id="story" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="display-5 fw-bold mb-4">Câu Chuyện Của Chúng Tôi</h2>
                <p class="text-muted fs-5">
                    Xuất phát từ nhu cầu thực tế của sinh viên và người lao động trẻ trong việc tìm kiếm
                    một nơi ở phù hợp, chúng tôi đã xây dựng nền tảng này với sứ mệnh kết nối mọi người
                    với những căn phòng lý tưởng.
                </p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 60px; height: 60px; background: var(--primary-gradient);">
                                <i class="fas fa-lightbulb text-white fs-4"></i>
                            </div>
                            <h3 class="card-title mb-0">Khởi Nguồn</h3>
                        </div>
                        <p class="card-text">
                            Năm 2020, nhóm sáng lập gồm các bạn sinh viên đã trải qua khó khăn trong việc
                            tìm kiếm phòng trọ phù hợp. Từ trải nghiệm cá nhân, chúng tôi quyết định
                            xây dựng một nền tảng giúp kết nối chủ nhà và người thuê một cách hiệu quả.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 60px; height: 60px; background: var(--accent-gradient);">
                                <i class="fas fa-rocket text-white fs-4"></i>
                            </div>
                            <h3 class="card-title mb-0">Phát Triển</h3>
                        </div>
                        <p class="card-text">
                            Trải qua 4 năm phát triển, chúng tôi đã phục vụ hơn 50,000 khách hàng và
                            hợp tác với hơn 5,000 chủ nhà trên toàn quốc. Nền tảng không ngừng được
                            cải tiến để mang lại trải nghiệm tốt nhất.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Mission & Vision -->
<section class="py-5" style="background-color: var(--light-color);">
    <div class="container">
        <div class="row g-5">
            <div class="col-lg-6">
                <div class="card border-0 h-100" style="background: var(--primary-gradient); color: white;">
                    <div class="card-body p-5">
                        <h3 class="card-title fs-1 fw-bold mb-4" style=" color: white;">
                            <i class="fas fa-bullseye me-3"></i>Sứ Mệnh
                        </h3>
                        <p class="card-text fs-5 lh-lg" style=" color: white;">
                            Kết nối mọi người với ngôi nhà lý tưởng, tạo ra một cộng đồng thuê phòng
                            minh bạch, đáng tin cậy và thuận tiện. Chúng tôi cam kết mang đến
                            những giải pháp an cư tốt nhất cho mọi đối tượng khách hàng.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card border-0 h-100" style="background: var(--secondary-gradient); color: white;">
                    <div class="card-body p-5" >
                        <h3 class="card-title fs-1 fw-bold mb-4" style=" color: white;">
                            <i class="fas fa-eye me-3" ></i>Tầm Nhìn
                        </h3>
                        <p class="card-text fs-5 lh-lg" style=" color: white;">
                            Trở thành nền tảng cho thuê phòng số 1 Việt Nam, tiên phong trong việc
                            ứng dụng công nghệ để cải thiện trải nghiệm khách hàng và xây dựng
                            một thị trường bất động sản cho thuê minh bạch, hiệu quả.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Values Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold mb-4">Giá Trị Cốt Lõi</h2>
            <p class="text-muted fs-5">
                Những giá trị định hướng mọi hoạt động của chúng tôi
            </p>
        </div>

        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 80px; height: 80px; background: var(--success-color);">
                        <i class="fas fa-shield-alt text-white fs-3"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Uy Tín</h4>
                    <p class="text-muted">
                        Xây dựng niềm tin thông qua sự minh bạch và cam kết chất lượng dịch vụ.
                    </p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 80px; height: 80px; background: var(--info-color);">
                        <i class="fas fa-users text-white fs-3"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Khách Hàng Là Trung Tâm</h4>
                    <p class="text-muted">
                        Đặt nhu cầu và trải nghiệm khách hàng làm ưu tiên hàng đầu trong mọi quyết định.
                    </p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 80px; height: 80px; background: var(--warning-color);">
                        <i class="fas fa-cogs text-white fs-3"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Đổi Mới</h4>
                    <p class="text-muted">
                        Không ngừng cải tiến và ứng dụng công nghệ để nâng cao chất lượng dịch vụ.
                    </p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 80px; height: 80px; background: var(--danger-color);">
                        <i class="fas fa-heart text-white fs-3"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Tận Tâm</h4>
                    <p class="text-muted">
                        Phục vụ khách hàng với sự nhiệt tình, chu đáo và trách nhiệm cao.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="py-5" style="background-color: var(--light-color);">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold mb-4">Đội Ngũ Lãnh Đạo</h2>
            <p class="text-muted fs-5">
                Những con người tạo nên sự khác biệt
            </p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="card text-center border-0">
                    <div class="position-relative">
                        <img src="/images/team-1.jpg" alt="CEO - Nguyễn Văn A" class="card-img-top rounded-circle mx-auto"
                             style="width: 200px; height: 200px; object-fit: cover;">
                        <div class="position-absolute bottom-0 start-50 translate-middle-x">
                            <span class="badge badge-primary">CEO & Founder</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h4 class="card-title fw-bold">Nguyễn Văn A</h4>
                        <p class="text-muted mb-3">10+ năm kinh nghiệm trong lĩnh vực bất động sản</p>
                        <div class="d-flex justify-content-center gap-2">
                            <a href="#" class="btn btn-sm" style="background: #1877f2; color: white;">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-sm" style="background: #0a66c2; color: white;">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card text-center border-0">
                    <div class="position-relative">
                        <img src="/images/team-2.jpg" alt="CTO - Trần Thị B" class="card-img-top rounded-circle mx-auto"
                             style="width: 200px; height: 200px; object-fit: cover;">
                        <div class="position-absolute bottom-0 start-50 translate-middle-x">
                            <span class="badge badge-info">CTO</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h4 class="card-title fw-bold">Trần Thị B</h4>
                        <p class="text-muted mb-3">Chuyên gia công nghệ với 8+ năm kinh nghiệm</p>
                        <div class="d-flex justify-content-center gap-2">
                            <a href="#" class="btn btn-sm" style="background: #1877f2; color: white;">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-sm" style="background: #0a66c2; color: white;">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card text-center border-0">
                    <div class="position-relative">
                        <img src="/images/team-3.jpg" alt="COO - Lê Văn C" class="card-img-top rounded-circle mx-auto"
                             style="width: 200px; height: 200px; object-fit: cover;">
                        <div class="position-absolute bottom-0 start-50 translate-middle-x">
                            <span class="badge badge-success">COO</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h4 class="card-title fw-bold">Lê Văn C</h4>
                        <p class="text-muted mb-3">Chuyên gia vận hành và phát triển kinh doanh</p>
                        <div class="d-flex justify-content-center gap-2">
                            <a href="#" class="btn btn-sm" style="background: #1877f2; color: white;">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-sm" style="background: #0a66c2; color: white;">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-5" style="background: var(--primary-gradient); color: white;">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold mb-4">Thành Tựu Của Chúng Tôi</h2>
            <p class="fs-5 opacity-75">
                Những con số ấn tượng sau 4 năm hoạt động
            </p>
        </div>

        <div class="row g-4 text-center">
            <div class="col-lg-3 col-md-6">
                <div class="p-4">
                    <div class="display-4 fw-bold mb-2">50K+</div>
                    <h5 class="fw-semibold">Khách Hàng Hài Lòng</h5>
                    <p class="opacity-75">Đã tìm được nơi ở lý tưởng</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="p-4">
                    <div class="display-4 fw-bold mb-2">5K+</div>
                    <h5 class="fw-semibold">Chủ Nhà Đối Tác</h5>
                    <p class="opacity-75">Tin tưởng và hợp tác lâu dài</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="p-4">
                    <div class="display-4 fw-bold mb-2">20K+</div>
                    <h5 class="fw-semibold">Phòng Trọ Chất Lượng</h5>
                    <p class="opacity-75">Được đăng tải và quản lý</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="p-4">
                    <div class="display-4 fw-bold mb-2">99%</div>
                    <h5 class="fw-semibold">Tỷ Lệ Hài Lòng</h5>
                    <p class="opacity-75">Từ phản hồi khách hàng</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-4">Sẵn Sàng Tìm Phòng Trọ Lý Tưởng?</h2>
                <p class="fs-5 text-muted mb-4">
                    Hãy để chúng tôi giúp bạn tìm được nơi ở hoàn hảo.
                    Với hàng nghìn lựa chọn chất lượng và dịch vụ tận tâm.
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="/Rooms" class="btn btn-primary btn-lg">Tìm Phòng Ngay</a>
                    <a href="/Contact" class="btn btn-outline-primary btn-lg">Liên Hệ Tư Vấn</a>
                </div>
            </div>
        </div>
    </div>
</section>
<style>
    .card-text {
        color: white;
        line-height: 1.6;
    }
</style>
<!-- Schema.org Structured Data -->
<script type="application/ld+json">
    @Html.Raw(@"{
      ""@context"": ""https://schema.org"",
      ""@type"": ""Organization"",
      ""name"": ""Dịch Vụ Cho Thuê Phòng Trọ"",
      ""url"": """ + $"{@Context.Request.Scheme}://{@Context.Request.Host}" + @""",
      ""logo"": """ + $"{@Context.Request.Scheme}://{@Context.Request.Host}/images/logo.png" + @""",
      ""description"": ""Đơn vị tiên phong trong dịch vụ cho thuê phòng trọ chất lượng cao tại Việt Nam"",
      ""foundingDate"": ""2020"",
      ""address"": {
        ""@type"": ""PostalAddress"",
        ""streetAddress"": ""123 Đường ABC"",
        ""addressLocality"": ""Quận 1"",
        ""addressRegion"": ""TP. Hồ Chí Minh"",
        ""addressCountry"": ""VN""
      },
      ""contactPoint"": {
        ""@type"": ""ContactPoint"",
        ""telephone"": ""+84-***********"",
        ""contactType"": ""Customer Service""
      },
      ""sameAs"": [
        ""https://facebook.com/yourpage"",
        ""https://linkedin.com/company/yourcompany""
      ]
    }")
</script>
