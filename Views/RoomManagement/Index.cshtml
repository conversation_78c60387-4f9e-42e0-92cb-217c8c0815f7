@{
    ViewData["Title"] = "Quản lý Phòng Trọ";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}

<div class="room-management">
    <div class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1><i class="fas fa-door-open"></i> Quản lý Phòng Trọ</h1>
                <p>Thê<PERSON>, sửa, xóa và quản lý các phòng trọ</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" id="addRoomBtn">
                    <i class="fas fa-plus"></i> Thêm phòng trọ
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <div class="filters-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Tì<PERSON> kiếm theo tên phòng...">
            </div>
            
            <div class="filter-group">
                <select id="nhaTroFilter">
                    <option value="">Tất cả nhà trọ</option>
                </select>
                
                <select id="theLoaiFilter">
                    <option value="">Tất cả loại phòng</option>
                </select>
                
                <select id="trangThaiFilter">
                    <option value="">Tất cả trạng thái</option>
                    <option value="true">Còn trống</option>
                    <option value="false">Đã thuê</option>
                </select>
                
                <button class="btn btn-secondary" id="clearFiltersBtn">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Rooms Table -->
    <div class="table-section">
        <div class="table-container">
            <div class="table-responsive">
                <table class="rooms-table">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Hình ảnh</th>
                            <th>Tên phòng</th>
                            <th>Nhà trọ</th>
                            <th>Loại phòng</th>
                            <th>Giá thuê</th>
                            <th>Diện tích</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="roomsTableBody">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Cards -->
        <div class="mobile-cards" id="mobileRoomCards">
            <!-- Mobile cards will be loaded here -->
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Hiển thị 0 - 0 của 0 phòng trọ</span>
                <div class="items-per-page">
                    <label for="itemsPerPage">Hiển thị:</label>
                    <select id="itemsPerPage">
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                    <span>phòng/trang</span>
                </div>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-outline" id="prevPageBtn" disabled>
                    <i class="fas fa-chevron-left"></i> Trước
                </button>
                <div class="page-numbers" id="pageNumbers"></div>
                <button class="btn btn-outline" id="nextPageBtn" disabled>
                    Sau <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Room Modal -->
<div class="modal" id="roomModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">
                <i class="fas fa-door-open"></i> 
                <span id="modalTitleText">Thêm phòng trọ</span>
            </h2>
            <button class="modal-close" id="closeModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="roomForm" class="modal-body" enctype="multipart/form-data">
            <div class="form-row">
                <div class="form-group">
                    <label for="tenPhong">Tên phòng <span class="required">*</span></label>
                    <input type="text" id="tenPhong" name="tenPhong" required 
                           placeholder="Nhập tên phòng">
                    <div class="error-message" id="tenPhongError"></div>
                </div>
                
                <div class="form-group">
                    <label for="maNhaTro">Nhà trọ <span class="required">*</span></label>
                    <select id="maNhaTro" name="maNhaTro" required>
                        <option value="">Chọn nhà trọ</option>
                    </select>
                    <div class="error-message" id="maNhaTroError"></div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="maTheLoai">Loại phòng <span class="required">*</span></label>
                    <select id="maTheLoai" name="maTheLoai" required>
                        <option value="">Chọn loại phòng</option>
                    </select>
                    <div class="error-message" id="maTheLoaiError"></div>
                </div>
                
                <div class="form-group">
                    <label for="gia">Giá thuê (VNĐ) <span class="required">*</span></label>
                    <input type="number" id="gia" name="gia" required min="0" step="1000"
                           placeholder="Nhập giá thuê">
                    <div class="error-message" id="giaError"></div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="dienTich">Diện tích (m²) <span class="required">*</span></label>
                    <input type="number" id="dienTich" name="dienTich" required min="0" step="0.1"
                           placeholder="Nhập diện tích">
                    <div class="error-message" id="dienTichError"></div>
                </div>
                
                <div class="form-group">
                    <label for="conTrong">Trạng thái <span class="required">*</span></label>
                    <select id="conTrong" name="conTrong" required>
                        <option value="">Chọn trạng thái</option>
                        <option value="true">Còn trống</option>
                        <option value="false">Đã thuê</option>
                    </select>
                    <div class="error-message" id="conTrongError"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="moTa">Mô tả</label>
                <textarea id="moTa" name="moTa" rows="4" 
                         placeholder="Nhập mô tả về phòng trọ (tùy chọn)"></textarea>
                <div class="error-message" id="moTaError"></div>
            </div>
            
            <div class="form-group">
                <label for="images">Hình ảnh</label>
                <input type="file" id="images" name="images" accept="image/*" multiple>
                <div class="file-info">
                    <small>Chấp nhận: JPG, PNG, GIF. Tối đa 5MB mỗi ảnh. Có thể chọn nhiều ảnh.</small>
                    <small id="imageUpdateNote" style="display: none; color: #f59e0b; font-weight: 600;">
                        <i class="fas fa-info-circle"></i> Lưu ý: Nếu chọn ảnh mới, tất cả ảnh cũ sẽ được thay thế. Bỏ trống nếu không muốn thay đổi ảnh.
                    </small>
                </div>
                <div class="error-message" id="imagesError"></div>
                
                <!-- Image Preview -->
                <div id="imagePreview" class="image-preview-container" style="display: none;">
                    <!-- Preview images will be shown here -->
                </div>
            </div>
        </form>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-primary" id="saveRoomBtn">
                <i class="fas fa-save"></i> <span id="saveBtnText">Lưu</span>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteModal">
    <div class="modal-content delete-modal">
        <div class="modal-header">
            <h2><i class="fas fa-exclamation-triangle text-danger"></i> Xác nhận xóa</h2>
            <button class="modal-close" id="closeDeleteModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <p>Bạn có chắc chắn muốn xóa phòng trọ <strong id="deleteRoomName"></strong> không?</p>
            <p class="text-danger"><i class="fas fa-warning"></i> Hành động này không thể hoàn tác!</p>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i> Xóa
            </button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Đang tải dữ liệu...</p>
    </div>
</div>

<!-- Camera View Modal -->
<div class="modal" id="cameraModal">
    <div class="modal-content camera-modal">
        <div class="modal-header">
            <h2><i class="fas fa-video"></i> Xem Camera</h2>
            <button class="modal-close" id="closeCameraModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <div class="camera-container">
                <div class="camera-info">
                    <h4 id="cameraRoomName">Phòng A101</h4>
                    <p id="cameraStatus" class="status-online">Camera đang hoạt động</p>
                </div>
                
                <div class="video-container">
                    <video id="cameraVideo" controls autoplay muted>
                        <source id="cameraVideoSource" src="" type="video/mp4">
                        Trình duyệt của bạn không hỗ trợ video tag.
                    </video>
                    
                    <!-- YouTube Video Container -->
                    <div id="youtubeContainer" class="youtube-container" style="display: none;">
                        <iframe id="youtubeVideo" 
                                width="100%" 
                                height="400" 
                                src="" 
                                frameborder="0" 
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                                allowfullscreen>
                        </iframe>
                    </div>
                    
                    <!-- Demo video for testing -->
                    <div id="demoVideo" class="demo-video" style="display: none;">
                        <video controls autoplay muted loop>
                            <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" type="video/mp4">
                            Demo video không khả dụng.
                        </video>
                        <div class="demo-overlay">
                            <div class="demo-info">
                                <i class="fas fa-play-circle"></i>
                                <h5>Demo Camera Stream</h5>
                                <p>Đây là video demo để test giao diện camera</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Fallback for browsers that don't support RTSP -->
                    <div id="cameraFallback" class="camera-fallback" style="display: none;">
                        <div class="fallback-content">
                            <i class="fas fa-video-slash"></i>
                            <h5>Không thể hiển thị video trực tiếp</h5>
                            <p>Trình duyệt không hỗ trợ RTSP stream. Vui lòng sử dụng ứng dụng camera riêng.</p>
                            <div class="camera-actions">
                                <button class="btn btn-primary" onclick="openCameraApp()">
                                    <i class="fas fa-external-link-alt"></i> Mở ứng dụng camera
                                </button>
                                <button class="btn btn-secondary" onclick="copyCameraUrl()">
                                    <i class="fas fa-copy"></i> Sao chép URL
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="camera-controls">
                    <div class="control-group">
                        <label>Camera URL:</label>
                        <div class="url-display">
                            <input type="text" id="cameraUrl" readonly value="rtsp://*************:554/stream">
                            <button class="btn btn-sm btn-outline-primary" onclick="copyCameraUrl()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <label>Trạng thái:</label>
                        <span id="connectionStatus" class="status-badge status-online">Kết nối</span>
                    </div>
                </div>
                
                <!-- Camera URL Input -->
                <div class="camera-url-input">
                    <div class="input-group">
                        <label for="customCameraUrl">Thêm URL Camera:</label>
                        <div class="url-input-container">
                            <input type="text" id="customCameraUrl" placeholder="Nhập URL camera (rtsp://, http://, https://)">
                            <button class="btn btn-primary" onclick="loadCustomCamera()">
                                <i class="fas fa-play"></i> Tải
                            </button>
                        </div>
                        <small class="url-help">
                            Ví dụ: rtsp://*************:554/stream hoặc http://camera-ip:8080/video
                        </small>
                        <div class="demo-urls">
                            <small><strong>URL Demo để test:</strong></small>
                            <div class="demo-url-buttons">
                                <button class="btn btn-sm btn-outline-secondary" onclick="loadDemoUrl('https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4')">
                                    Demo MP4
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="loadDemoUrl('https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4')">
                                    Demo Video 2
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="loadDemoUrl('https://www.w3schools.com/html/mov_bbb.mp4')">
                                    Demo Video 3
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="loadYouTubeVideo('https://www.youtube.com/watch?v=--INr5Q55MQ')">
                                    🎥 YouTube Demo
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="closeCameraBtn">
                <i class="fas fa-times"></i> Đóng
            </button>
            <button type="button" class="btn btn-primary" id="refreshCameraBtn">
                <i class="fas fa-sync-alt"></i> Làm mới
            </button>
        </div>
    </div>
</div>

<style>
    /* Ensure modal is properly displayed */
    .modal {
        z-index: 99999 !important;
        align-items: flex-start !important;
        justify-content: center !important;
        padding: 20px !important;
        padding-top: 10px !important;
        padding-bottom: 60px !important;
        overflow-y: auto !important;
    }
    
    .modal-content {
        max-height: 80vh !important;
        overflow: visible !important;
        margin: 0 auto 50px auto !important;
        position: relative !important;
        top: 0 !important;
        display: flex !important;
        flex-direction: column !important;
    }
    
    .modal-body {
        max-height: calc(80vh - 160px) !important;
        overflow-y: auto !important;
        flex: 1 !important;
    }
    
    .modal-footer {
        position: sticky !important;
        bottom: 0 !important;
        background: #f8fafc !important;
        border-top: 1px solid #e5e7eb !important;
        z-index: 100 !important;
        flex-shrink: 0 !important;
        margin-top: auto !important;
    }
    
    /* Desktop specific */
    @@media (min-width: 769px) {
        .modal {
            align-items: flex-start !important;
            justify-content: center !important;
            padding: 30px !important;
            padding-top: 20px !important;
            padding-bottom: 80px !important;
        }
        
        .modal-content {
            width: 700px !important;
            max-width: 90vw !important;
        }
        
        .camera-modal {
            width: 900px !important;
            max-width: 95vw !important;
        }
    }
    
    /* Camera Modal Styles */
    .camera-container {
        padding: 20px;
    }
    
    .camera-info {
        margin-bottom: 20px;
        text-align: center;
    }
    
    .camera-info h4 {
        color: #333;
        margin-bottom: 5px;
    }
    
    .camera-info p {
        color: #666;
        margin: 0;
    }
    
    .video-container {
        position: relative;
        background: #000;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 20px;
        min-height: 300px;
    }
    
    #cameraVideo {
        width: 100%;
        height: 400px;
        background: #000;
        object-fit: cover;
    }
    
    .camera-fallback {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }
    
    .demo-video {
        position: relative;
        width: 100%;
        height: 400px;
        background: #000;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .demo-video video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .demo-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }
    
    .demo-info {
        text-align: center;
        padding: 20px;
    }
    
    .demo-info i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #17a2b8;
    }
    
    .demo-info h5 {
        margin-bottom: 10px;
        font-size: 1.2rem;
    }
    
    .demo-info p {
        margin: 0;
        opacity: 0.8;
        font-size: 0.9rem;
    }
    
    .youtube-container {
        width: 100%;
        height: 400px;
        background: #000;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }
    
    .youtube-container iframe {
        width: 100%;
        height: 100%;
        border: none;
    }
    
    .fallback-content {
        text-align: center;
        padding: 20px;
    }
    
    .fallback-content i {
        font-size: 3rem;
        margin-bottom: 20px;
        color: #ffc107;
    }
    
    .fallback-content h5 {
        margin-bottom: 10px;
    }
    
    .fallback-content p {
        margin-bottom: 20px;
        opacity: 0.8;
    }
    
    .camera-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .camera-controls {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 20px;
        align-items: center;
    }
    
    .control-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    
    .control-group label {
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
    }
    
    .url-display {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .url-display input {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 0.9rem;
        background: #f8f9fa;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-online {
        background: #d4edda;
        color: #155724;
    }
    
    .status-offline {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-connecting {
        background: #fff3cd;
        color: #856404;
    }
    
    /* Camera URL Input */
    .camera-url-input {
        margin-top: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        border: 1px solid #e9ecef;
    }
    
    .camera-url-input .input-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .camera-url-input label {
        font-weight: 600;
        color: #333;
        font-size: 0.95rem;
    }
    
    .url-input-container {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .url-input-container input {
        flex: 1;
        padding: 10px 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    
    .url-input-container input:focus {
        border-color: #17a2b8;
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
    }
    
    .url-input-container .btn {
        padding: 10px 15px;
        font-size: 0.9rem;
        white-space: nowrap;
    }
    
    .url-help {
        color: #6c757d;
        font-size: 0.8rem;
        margin-top: 5px;
    }
    
    .demo-urls {
        margin-top: 10px;
    }
    
    .demo-urls small {
        display: block;
        margin-bottom: 8px;
        color: #495057;
    }
    
    .demo-url-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }
    
    .demo-url-buttons .btn {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
    
    /* Mobile responsive for camera modal */
    @@media (max-width: 768px) {
        .camera-controls {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        
        .camera-actions {
            flex-direction: column;
        }
        
        .camera-actions .btn {
            width: 100%;
        }
        
        #cameraVideo {
            height: 250px;
        }
        
        .url-input-container {
            flex-direction: column;
            align-items: stretch;
        }
        
        .url-input-container .btn {
            width: 100%;
        }
    }
            max-height: 75vh !important;
            margin: 0 auto 50px auto !important;
        }
        
        .modal-body {
            max-height: calc(75vh - 160px) !important;
        }
    }
    
    /* Mobile specific fixes */
    @@media (max-width: 768px) {
        .modal {
            align-items: flex-start !important;
            justify-content: center !important;
            padding: 15px !important;
            padding-top: 10px !important;
            padding-bottom: 50px !important;
        }
        
        .modal-content {
            margin: 0 auto !important;
            width: 100% !important;
            max-width: none !important;
            max-height: 82vh !important;
        }
        
        .modal-body {
            max-height: calc(82vh - 160px) !important;
        }
        
        .modal-footer {
            flex-direction: column !important;
            gap: 10px !important;
        }
        
        .modal-footer .btn {
            width: 100% !important;
            min-height: 50px !important;
            font-size: 16px !important;
        }
    }
    
    @@media (max-width: 480px) {
        .modal {
            padding: 10px !important;
            padding-top: 5px !important;
            padding-bottom: 40px !important;
        }
        
        .modal-content {
            max-height: 85vh !important;
        }
        
        .modal-body {
            max-height: calc(85vh - 140px) !important;
        }
        
        .modal-footer .btn {
            min-height: 52px !important;
            font-weight: 700 !important;
        }
    }
    
    /* Form row layout */
    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-row .form-group {
        flex: 1;
        margin-bottom: 0;
    }
    
    @@media (max-width: 768px) {
        .form-row {
            flex-direction: column;
            gap: 0;
        }
        
        .form-row .form-group {
            margin-bottom: 20px;
        }
    }
    
    /* Image preview container */
    .image-preview-container {
        margin-top: 15px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .image-preview-item {
        position: relative;
        display: inline-block;
    }
    
    .image-preview-item img {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #e5e7eb;
    }
    
    .image-preview-item .remove-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #ef4444;
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
    }
    
    .pagination-info {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }
    
    .items-per-page {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
    }
    
    .items-per-page label {
        margin: 0;
        font-weight: 500;
        color: #6b7280;
    }
    
    .items-per-page select {
        padding: 4px 8px;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        background: white;
        font-size: 14px;
        min-width: 60px;
    }
    
    .items-per-page select:focus {
        outline: none;
        border-color: #4a90a4;
        box-shadow: 0 0 0 2px rgba(74, 144, 164, 0.1);
    }
    
    .items-per-page span {
        color: #6b7280;
    }
    
    @@media (max-width: 768px) {
        .pagination-info {
            justify-content: center;
            gap: 15px;
        }
        
        .items-per-page {
            font-size: 13px;
        }
    }
    
    /* Form styling for disabled fields */
    .form-group select:disabled,
    .form-group input:disabled {
        background-color: #f3f4f6 !important;
        color: #6b7280 !important;
        cursor: not-allowed !important;
        opacity: 0.7;
    }
    
    .form-group select:disabled + label::after,
    .form-group input:disabled + label::after {
        content: " (không thể thay đổi)";
        font-size: 0.75rem;
        color: #9ca3af;
        font-weight: normal;
    }
</style>

<link href="~/css/room-management.css?@DateTime.Now" rel="stylesheet">
<script src="~/js/room-management.js?@DateTime.Now"></script> 