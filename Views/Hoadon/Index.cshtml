@{
    ViewData["Title"] = "Quản lý hóa đơn tiện ích";
    Layout = "~/Views/Shared/_Layoutadmin.cshtml";
}
<link href="~/css/hoadon.css?v=@DateTime.Now" rel="stylesheet" />
<div class="utility-bill-management">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1><i class="fas fa-file-invoice-dollar"></i> Quản lý hóa đơn tiện ích</h1>
                <p><PERSON>h<PERSON><PERSON>, sử<PERSON>, xóa và quản lý các hóa đơn tiện ích của phòng trọ</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" id="addBillBtn">
                    <i class="fas fa-plus"></i> Thêm hóa đơn mới
                </button>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <div class="filters-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Tìm kiếm theo mã hóa đơn, phòng, tháng/năm...">
            </div>

            <div class="filter-group">
                <select id="roomFilter">
                    <option value="">Tất cả phòng</option>
                </select>

                <select id="statusFilter">
                    <option value="">Tất cả trạng thái</option>
                    <option value="true">Đã thanh toán</option>
                    <option value="false">Chưa thanh toán</option>
                </select>

                <button class="btn btn-secondary" id="clearFiltersBtn">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Bills Table -->
    <div class="table-section">
        <div class="table-container">
            <div class="table-responsive">
                <table class="bills-table">
                    <thead>
                        <tr>
                            <th>Mã HĐ</th>
                            <th>Phòng</th>
                            <th>Tháng/Năm</th>
                            <th>Điện (kWh)</th>
                            <th>Nước (m³)</th>
                            <th>Phí DV</th>
                            <th>Phí dữ xe</th>
                            <th>Tổng tiền</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="billsTableBody">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Cards -->
        <div class="mobile-cards" id="mobileBillCards">
            <!-- Mobile cards will be loaded here -->
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Hiển thị 0 - 0 của 0 hóa đơn</span>
                <div class="items-per-page">
                    <label for="itemsPerPage">Hiển thị:</label>
                    <select id="itemsPerPage">
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                    <span>hóa đơn/trang</span>
                </div>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-outline" id="prevPageBtn" disabled>
                    <i class="fas fa-chevron-left"></i> Trước
                </button>
                <div class="page-numbers" id="pageNumbers"></div>
                <button class="btn btn-outline" id="nextPageBtn" disabled>
                    Sau <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Bill Modal -->
<div class="modal" id="billModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">
                <i class="fas fa-file-invoice-dollar"></i>
                <span id="modalTitleText">Thêm hóa đơn mới</span>
            </h2>
            <button class="modal-close" id="closeModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="billForm" class="modal-body">
            <div class="form-row">
                <div class="form-group">
                    <label for="maPhong">Phòng <span class="required">*</span></label>
                    <select id="maPhong" name="maPhong" required>
                        <option value="">Chọn phòng</option>
                    </select>
                    <div class="error-message" id="maPhongError"></div>
                </div>

                <div class="form-group">
                    <label for="soDien">Số điện (kWh) <span class="required">*</span></label>
                    <input type="number" id="soDien" name="soDien" required min="0" step="0.1"
                           placeholder="Nhập số điện">
                    <div class="error-message" id="soDienError"></div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="soNuoc">Số nước (m³) <span class="required">*</span></label>
                    <input type="number" id="soNuoc" name="soNuoc" required min="0" step="0.1"
                           placeholder="Nhập số nước">
                    <div class="error-message" id="soNuocError"></div>
                </div>

                <div class="form-group">
                    <label for="note">Ghi chú</label>
                    <textarea id="note" name="note" rows="3"
                              placeholder="Nhập ghi chú (nếu có)"></textarea>
                    <div class="error-message" id="noteError"></div>
                </div>
            </div>

            <div class="form-group" id="statusGroup" style="display: none;">
                <label for="daThanhToan">Trạng thái thanh toán</label>
                <select id="daThanhToan" name="daThanhToan">
                    <option value="false">Chưa thanh toán</option>
                    <option value="true">Đã thanh toán</option>
                </select>
                <div class="error-message" id="daThanhToanError"></div>
            </div>
        </form>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-primary" id="saveBillBtn">
                <i class="fas fa-save"></i> <span id="saveBtnText">Lưu</span>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteModal">
    <div class="modal-content delete-modal">
        <div class="modal-header">
            <h2><i class="fas fa-exclamation-triangle text-danger"></i> Xác nhận xóa</h2>
            <button class="modal-close" id="closeDeleteModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
            <p>Bạn có chắc chắn muốn xóa hóa đơn <strong id="deleteBillId"></strong> không?</p>
            <p class="text-danger"><i class="fas fa-warning"></i> Hành động này sẽ xóa tất cả dữ liệu liên quan và không thể hoàn tác!</p>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">
                <i class="fas fa-times"></i> Hủy
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i> Xóa
            </button>
        </div>
    </div>
</div>

<!-- Print Bill Modal -->
<div class="modal" id="printModal">
    <div class="modal-content print-modal">
        <div class="modal-header">
            <h2><i class="fas fa-print"></i> Xem hóa đơn</h2>
            <button class="modal-close" id="closePrintModalBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body" id="printModalBody">
            <!-- Bill content will be loaded here -->
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelPrintBtn">
                <i class="fas fa-times"></i> Đóng
            </button>
            <button type="button" class="btn btn-primary" id="printBillBtn">
                <i class="fas fa-print"></i> In hóa đơn
            </button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Đang tải dữ liệu...</p>
    </div>
</div>

<style>
    /* Enhanced table for better information display */
    .bills-table th:nth-child(2),
    .bills-table td:nth-child(2) {
        min-width: 150px;
    }

    .bills-table th:nth-child(8),
    .bills-table td:nth-child(8) {
        min-width: 120px;
    }

    .bills-table th:nth-child(9),
    .bills-table td:nth-child(9) {
        min-width: 120px;
    }

    .room-info {
        display: flex;
        flex-direction: column;
    }

    .room-main {
        font-weight: 600;
        color: #1f2937;
    }

    .room-details {
        font-size: 12px;
        color: #6b7280;
        margin-top: 2px;
    }

    .text-muted {
        color: #6b7280 !important;
        font-size: 0.875rem;
    }

    .utility-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

        .utility-info span {
            font-size: 13px;
        }

    .amount-info {
        font-weight: 600;
        color: #059669;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        min-width: 80px;
    }

    .status-paid {
        background-color: #dcfce7;
        color: #166534;
    }

    .status-unpaid {
        background-color: #fef3c7;
        color: #92400e;
    }

    /* Print modal specific styling */
    .print-modal .modal-content {
        max-width: 850px !important;
    }

    .print-modal .modal-body {
        padding: 30px;
    }

    /* Form row layout */
    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

    @@media (max-width: 768px) {
        .form-row {
            flex-direction: column;
            gap: 0;
        }

            .form-row .form-group {
                margin-bottom: 20px;
            }
    }

    /* Print modal specific styles */
    .print-modal .modal-content {
        width: 800px !important;
        max-width: 95vw !important;
    }

    .print-modal .modal-body {
        padding: 20px;
        background: white;
    }

    /* Pagination styles */
    .pagination-info {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .items-per-page {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
    }

        .items-per-page label {
            margin: 0;
            font-weight: 500;
            color: #6b7280;
        }

        .items-per-page select {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            font-size: 14px;
            min-width: 60px;
        }

            .items-per-page select:focus {
                outline: none;
                border-color: #4a90a4;
                box-shadow: 0 0 0 2px rgba(74, 144, 164, 0.1);
            }

        .items-per-page span {
            color: #6b7280;
        }

    @@media (max-width: 768px) {
        .pagination-info {
            justify-content: center;
            gap: 15px;
        }

        .items-per-page {
            font-size: 13px;
        }
    }
</style>
<script src="~/js/hoadon.js?v=@DateTime.Now"></script>