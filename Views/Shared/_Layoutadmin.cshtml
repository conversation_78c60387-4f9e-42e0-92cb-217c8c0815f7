﻿@{
    ViewData["Title"] = "Trang Chủ";
    var Email = Context.Items["Email"];
    var role = Context.Items["role"];
    var id = Context.Items["id"];
    var name = Context.Items["CurrentUser"];
}
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Hệ thống quản lý nhà trọ - Admin Dashboard">
    <meta name="keywords" content="quản lý nhà trọ, admin dashboard, hệ thống quản lý">
    <meta name="author" content="Ql_NhaTro_jun">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Admin Dashboard - Quản lý nhà trọ">
    <meta property="og:description" content="<PERSON><PERSON> thống quản lý nhà trọ chuyên nghiệp">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico">
    
    <title>@ViewData["Title"] - Admin Dashboard</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Stylesheets -->

    <link href="~/css/admin.css?@DateTime.Now" rel="stylesheet">
    <link href="~/css/dashboard.css?@DateTime.Now" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <link href="~/css/responsive-enhancements.css?@DateTime.Now" rel="stylesheet">
    
    
    
    @await RenderSectionAsync("Styles", required: false)
    @RenderSection("head", required: false)
    <!-- Toastr & SweetAlert2 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
</head>

<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar" role="navigation" aria-label="Main navigation">
            <div class="sidebar-header">
                @if (role != null && role.ToString() == "2")
                {
                    <h1 class="h3">Admin Panel</h1>
                }
                else if (role != null && role.ToString() == "1")
                {
                    <h1 class="h3">Quản lý Pannel</h1>
                }
                else
                {
                    <h1 class="h3">Khác hàng Panel</h1>
                }
         
                <strong aria-hidden="true"><i class="fas fa-layer-group"></i></strong>
                <button type="button" id="sidebarClose" class="btn-close-mobile" aria-label="Close sidebar">
                    <i class="fas fa-times" aria-hidden="true"></i>
                </button>
            </div>

            <div class="admin-profile">
                <div class="admin-avatar" role="img" aria-label="User avatar">
                    <i class="fas fa-user" aria-hidden="true"></i>
                </div>
                <div class="admin-info">
                    <h2 class="h4">@(name ?? "Khách hàng")</h2>
                    <p>
                        @if(role != null && role.ToString() == "2")
                        {
                            <span class="badge badge-success" role="status">Admin</span>
                        }
                        else if(role != null && role.ToString() == "1")
                        {
                            <span class="badge badge-primary" role="status">Quản Lý</span>
                        }
                        else
                        {
                            <span class="badge badge-secondary" role="status">Khách hàng</span>
                        }
                    </p>
                </div>
            </div>

            <div class="menu-scroll-container">
                <ul class="list-unstyled components" role="menu">
                    <li role="none">
                        <a href="@Url.Action("Dashborad", "Nguoidungs")" role="menuitem">
                            <div class="menu-icon-wrapper">
                                <i class="fas fa-chart-line" aria-hidden="true"></i>
                            </div>
                        <span>Dashboard</span>
                    </a>
                </li>
                    
                    <li role="none">
                        <a href="@Url.Action("Index", "NguoiDungs")" role="menuitem">
                            <div class="menu-icon-wrapper">
                                <i class="fas fa-users-cog" aria-hidden="true"></i>
                            </div>
                            <span>Quản lý Tài Khoản</span>
                        </a>
                    </li>

                    @if (role.ToString() == "0")
                    {
                        <!-- Menu cho Khách hàng -->
                        <li class="menu-section">
                            <span class="menu-section-title">Khách hàng</span>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("HoaDonTong", "Users")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-file-invoice-dollar" aria-hidden="true"></i>
                                </div>
                                <span>Quản Lý Hóa Đơn</span>
                            </a>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Contract", "Users")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-file-contract" aria-hidden="true"></i>
                                </div>
                                <span>Xem Hợp Đồng</span>
                            </a>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Denbu", "Users")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-hand-holding-usd" aria-hidden="true"></i>
                                </div>
                                <span>Đền bù của tôi</span>
                            </a>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Chatbot", "Users")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-comments" aria-hidden="true"></i>
                                </div>
                                <span>Hòm thư</span>
                            </a>
                        </li>
                    }
                    @if (role.ToString() != "0")
                    {
                        <!-- Menu cho Admin/Quản lý -->
                        <li class="menu-section">
                            <span class="menu-section-title">Quản lý chính</span>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("QuanlyUser", "NguoiDungs")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-users" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Khách Hàng</span>
                            </a>
                        </li>
                        @if (role.ToString() == "2")
                        {
                                                            <li role="none">
                                    <a href="/vitri/TinhThanh" role="menuitem">
                                        <div class="menu-icon-wrapper">
                                            <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                        </div>
                                        <span>Quản lý Tỉnh thành</span>
                                    </a>
                                </li>

                                <li role="none">
                                    <a href="/vitri/KhuVuc" role="menuitem">
                                        <div class="menu-icon-wrapper">
                                            <i class="fas fa-map" aria-hidden="true"></i>
                                        </div>
                                        <span>Quản lý Khu vực</span>
                                    </a>
                                </li>

                            <li role="none">
                                <a href="@Url.Action("Index", "MotelManagement")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-building" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Nhà Trọ</span>
                            </a>
                        </li>
                        }
                        <li role="none">
                            <a href="@Url.Action("Index", "RoomTypeManagement")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-home" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Loại Phòng</span>
                            </a>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Index", "RoomManagement")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-door-open" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Phòng Trọ</span>
                            </a>
                        </li>
                        
                        <li class="menu-section">
                            <span class="menu-section-title">Tài chính & Hợp đồng</span>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Index", "ContractManagement")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-handshake" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Hợp Đồng</span>
                            </a>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Denbu", "NguoiDungs")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-money-check-alt" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Đền Bù</span>
                            </a>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Index", "HoaDon")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-file-invoice-dollar" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Hóa Đơn</span>
                            </a>
                        </li>
                        @if (role.ToString() == "2")
                        {
                            <li role="none">
                                <a href="@Url.Action("Index", "Banking")" role="menuitem">
                                    <div class="menu-icon-wrapper">
                                        <i class="fas fa-credit-card" aria-hidden="true"></i>
                                    </div>
                                    <span>Quản lý Bank</span>
                                </a>
                            </li>
                        }
                        <li class="menu-section">
                            <span class="menu-section-title">Hệ thống</span>
                        </li>
                       
                        <li role="none">
                            <a href="@Url.Action("Index", "WebSettings")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-globe" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Trang Web</span>
                            </a>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Banner", "NguoiDungs")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-tag" aria-hidden="true"></i>
                                </div>
                                <span>Quản lý Banner</span>
                            </a>
                        </li>
                        <li role="none">
                            <a href="@Url.Action("Chatbot", "Users")" role="menuitem">
                                <div class="menu-icon-wrapper">
                                    <i class="fas fa-comments" aria-hidden="true"></i>
                                </div>
                                <span>Hòm thư</span>
                            </a>
                        </li>
                       
                    }
                </ul>
            </div>

                    <ul class="list-unstyled sidebar-footer" role="menu">
                        <li role="none">
                    <a href="@Url.Action("Index", "Home")" role="menuitem">
                        <i class="fas fa-cog" aria-hidden="true"></i>
                        <span>Quay lại Home</span>
                    </a>
                </li>
                <li role="none">
                    <a href="#" id="logoutBtn" role="menuitem">
                        <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                        <span>Đăng Xuất</span>
                    </a>
                </li>
            </ul>
                
        </nav>

        <!-- Page Content -->
        <main id="content" role="main">
            <!-- Top Navbar -->
            <nav class="navbar navbar-expand" role="navigation" aria-label="Top navigation">
                <div class="container-fluid" style="
    margin-left: 0px;
">
                    <button type="button" id="sidebarCollapse" class="btn" aria-label="Toggle sidebar" aria-expanded="false">
                        <i class="fas fa-bars" aria-hidden="true"></i>
                    </button>
                </div>
            </nav>

            <!-- Main Content -->
            <div class="container-fluid animate-fade-in-up">
                @RenderBody()
            </div>
        </main>
    </div>

    @RenderSection("Modals", required: false)

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        document.body.addEventListener('click', function (e) {
            if (e.target && e.target.id === 'logoutBtn') {
                e.preventDefault();

                (async () => {
                    try {
                        const response = await fetch('/api/Auth/Logout', {
                            method: 'POST',
                            credentials: 'include',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            location.reload();
                        } else {
                            alert('Lỗi khi đăng xuất');
                        }
                    } catch {
                        alert('Lỗi kết nối API đăng xuất');
                    }
                })();
            }
        });
        // Toastr default settings
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        document.addEventListener('DOMContentLoaded', function () {
            const sidebarCollapse = document.getElementById('sidebarCollapse');
            const sidebarClose = document.getElementById('sidebarClose');
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');

            // Function to toggle sidebar
            function toggleSidebar(isOpen) {
                if (isOpen) {
                    sidebar.classList.add('active');
                    content.classList.add('active');
                    document.body.classList.add('sidebar-open');
                    sidebarCollapse.setAttribute('aria-expanded', 'true');
                } else {
                    sidebar.classList.remove('active');
                    content.classList.remove('active');
                    document.body.classList.remove('sidebar-open');
                    sidebarCollapse.setAttribute('aria-expanded', 'false');
                }
            }

            // Sidebar toggle with animation
            sidebarCollapse.addEventListener('click', function () {
                const isOpen = !sidebar.classList.contains('active');
                toggleSidebar(isOpen);
                
                // Add ripple effect
                const ripple = document.createElement('span');
                ripple.classList.add('ripple');
                this.appendChild(ripple);
                setTimeout(() => ripple.remove(), 300);
            });

            // Close sidebar button (mobile)
            sidebarClose.addEventListener('click', function () {
                toggleSidebar(false);
            });

            // Active menu item based on current URL
            const currentPath = window.location.pathname.toLowerCase();
            const currentController = '@ViewContext.RouteData.Values["controller"]'.toLowerCase();
            const currentAction = '@ViewContext.RouteData.Values["action"]'.toLowerCase();
            
            console.log('Current Path:', currentPath);
            console.log('Current Controller:', currentController);
            console.log('Current Action:', currentAction);
            
            // Remove all active classes first
            document.querySelectorAll('#sidebar ul.components li').forEach(function(li) {
                li.classList.remove('active');
                const link = li.querySelector('a');
                if (link) {
                    link.removeAttribute('aria-current');
                }
            });
            
            // Find and set active menu item
            let activeFound = false;
            document.querySelectorAll('#sidebar ul.components li a').forEach(function(link) {
                if (activeFound) return; // Only set one active item
                
                const href = link.getAttribute('href')?.toLowerCase() || '';
                console.log('Checking link:', href);
                
                // Exact path match (highest priority)
                if (href === currentPath) {
                    link.parentElement.classList.add('active');
                    link.setAttribute('aria-current', 'page');
                    activeFound = true;
                    console.log('✓ Exact path match:', href);
                    return;
                }
                
                // Controller + Action match
                if (href.includes('/' + currentController + '/') && href.includes('/' + currentAction)) {
                    link.parentElement.classList.add('active');
                    link.setAttribute('aria-current', 'page');
                    activeFound = true;
                    console.log('✓ Controller+Action match:', href);
                    return;
                }
                
                // Controller match (for Index actions)
                if (currentAction === 'index' && href.includes('/' + currentController)) {
                    // Make sure it's not a different action
                    if (!href.includes('/' + currentController + '/') || href.endsWith('/' + currentController)) {
                        link.parentElement.classList.add('active');
                        link.setAttribute('aria-current', 'page');
                        activeFound = true;
                        console.log('✓ Controller match (Index):', href);
                        return;
                    }
                }
            });
            
            if (!activeFound) {
                console.log('⚠ No active menu item found for:', currentPath);
            }

            // Add hover effects to menu items
            document.querySelectorAll('#sidebar ul li a').forEach(function(link) {
                link.addEventListener('mouseenter', function() {
                    if (!this.parentElement.classList.contains('active')) {
                        this.style.transform = 'translateX(5px)';
                    }
                });
                
                link.addEventListener('mouseleave', function() {
                    if (!this.parentElement.classList.contains('active')) {
                        this.style.transform = 'translateX(0)';
                    }
                });
            });

            // Auto-hide sidebar on mobile when clicking outside or on overlay
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 992) {
                    if (!sidebar.contains(e.target) && !sidebarCollapse.contains(e.target)) {
                        toggleSidebar(false);
                    }
                }
            });

            // Close sidebar when clicking on overlay
            document.body.addEventListener('click', function(e) {
                if (e.target === document.body && document.body.classList.contains('sidebar-open')) {
                    toggleSidebar(false);
                }
            });

            // Add smooth scroll behavior
            document.documentElement.style.scrollBehavior = 'smooth';
            
            // Initialize animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.animate-fade-in-up').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
                observer.observe(el);
            });
        });
    </script>

    @RenderSection("scripts", required: false)
</body>
</html>