﻿@model Ql_NhaTro_jun.Models.UserInfo;
@{
    ViewData["Title"] = "Trang Chủ";
    var user = Context.Items["CurrentUser"];
}

<!DOCTYPE html>
<html lang="vi" id="htmlRoot">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Phòng Trọ Việt - Tìm Phòng Trọ Uy Tín #1 Việt Nam</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Phòng Trọ Việt - Nền tảng tìm phòng trọ uy tín #1 Việt Nam. Hàng nghìn phòng trọ giá rẻ, chất lượng tại TP.HCM. Đăng tin miễn phí, tìm phòng nhanh chóng." />
    <meta name="keywords" content="phòng trọ, nhà trọ, cho thu<PERSON> phòng, tìm phòng trọ, phòng trọ giá rẻ, TP.HC<PERSON>, phòng trọ sinh viên" />
    <meta name="author" content="Phòng Trọ Việt" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="https://phongtroviet.vn" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Phòng Trọ Việt - Tìm Phòng Trọ Uy Tín" />
    <meta property="og:description" content="Nền tảng kết nối người thuê và chủ nhà uy tín với hàng nghìn lựa chọn chất lượng" />
    <meta property="og:image" content="~/images/og-image.jpg" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://phongtroviet.vn" />
    <meta property="og:site_name" content="Phòng Trọ Việt" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Phòng Trọ Việt - Tìm Phòng Trọ Uy Tín" />
    <meta name="twitter:description" content="Nền tảng tìm phòng trọ uy tín #1 Việt Nam" />
    <meta name="twitter:image" content="~/images/og-image.jpg" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="~/images/apple-touch-icon.png" />

    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="~/css/responsive-enhancements.css?v=@DateTime.Now.Ticks" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/site.css?v=@DateTime.Now.Ticks" asp-append-version="true" />

    @await RenderSectionAsync("Styles", required: false)

    <!-- Schema Markup -->
    <script type="application/ld+json">
        {
            "@@context": "https://schema.org",
            "@@type": "RealEstateAgent",
            "name": "Phòng Trọ Việt",
            "description": "Nền tảng kết nối người thuê và chủ nhà uy tín nhất Việt Nam",
            "url": "https://phongtroviet.vn",
            "logo": "https://phongtroviet.vn/images/logo.png",
            "address": {
                "@@type": "PostalAddress",
                "streetAddress": "123 Nguyễn Văn Linh",
                "addressLocality": "Quận 7",
                "addressRegion": "TP. Hồ Chí Minh",
                "postalCode": "700000",
                "addressCountry": "VN"
            },
            "telephone": "+84123456789",
            "email": "<EMAIL>",
            "sameAs": [
                "https://facebook.com/phongtroviet",
                "https://instagram.com/phongtroviet"
            ]
        }
    </script>
</head>

<body class="@ViewData["BodyClass"]">
   
    <header role="banner">
        <nav class="navbar navbar-expand-lg navbar-dark custom-navbar fixed-top" role="navigation" aria-label="Main navigation">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index" aria-label="Phòng Trọ Việt - Trang chủ">
                    <img src="https://scontent.fsgn5-10.fna.fbcdn.net/v/t1.15752-9/507992281_1159511539318008_228326955076242083_n.png?_nc_cat=107&ccb=1-7&_nc_sid=9f807c&_nc_ohc=L593ZVK22DoQ7kNvwHR2Sk3&_nc_oc=AdmjLct3M4bCbfhU_FAat5T2VU65mAvg24WSX0Gv_0CD_ZBeynERhhhynTRvrv3nIb8&_nc_zt=23&_nc_ht=scontent.fsgn5-10.fna&oh=03_Q7cD2wGmZ9euq27sFWIU0Rb6F6LVYehwJ3kM42yRAOzaLZQveQ&oe=68AC7CDB" alt="Logo Phòng Trọ Việt" height="45" width="auto" loading="eager" />
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Mở menu điều hướng">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto" role="menubar">
                        <li class="nav-item" role="none">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index" role="menuitem">
                                <i class="fas fa-home me-2" aria-hidden="true"></i>Trang Chủ
                            </a>
                        </li>
                        <li class="nav-item" role="none">
                            <a class="nav-link" asp-area="" asp-controller="phongtro" asp-action="Index" role="menuitem">
                                <i class="fas fa-search me-2" aria-hidden="true"></i>Tìm Phòng
                            </a>
                        </li>
                        <li class="nav-item" role="none">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="About" role="menuitem">
                                <i class="fas fa-info-circle me-2" aria-hidden="true"></i>Về Chúng Tôi
                            </a>
                        </li>
                        <li class="nav-item" role="none">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Contact" role="menuitem">
                                <i class="fas fa-envelope me-2" aria-hidden="true"></i>Liên Hệ
                            </a>
                        </li>
                     
                    </ul>

                    <ul class="navbar-nav" role="menubar">
                        <li class="nav-item" role="none">
                            @if (User.Identity.IsAuthenticated)
                            {
                                <div class="dropdown user-dropdown">
                                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button"
                                       data-bs-toggle="dropdown" aria-expanded="false" aria-haspopup="true" aria-label="Menu người dùng">
                                        <i class="fas fa-user-circle me-2" aria-hidden="true"></i>
                                        <span>Xin chào, @(user ?? "User")</span>
                                       
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" role="menu">
                                        <li role="none">
                                            <a class="dropdown-item" href="/Nguoidungs"  role="menuitem">
                                                <i class="fas fa-user me-2" aria-hidden="true"></i>
                                                @(user ?? "User")
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider" role="separator"></li>
                                        
                                        <li role="none">
                                            <a href="#" class="dropdown-item" id="logoutBtn" role="menuitem">
                                                <i class="fas fa-sign-out-alt me-2" aria-hidden="true"></i>
                                                Đăng Xuất
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            }
                            else
                            {
                                <a class="nav-link" asp-area="" asp-controller="Users" asp-action="Login" role="menuitem">
                                    <i class="fas fa-sign-in-alt me-2" aria-hidden="true"></i>
                                    <span class="d-none d-md-inline">Đăng Nhập</span>
                                    <span class="d-md-none">Login</span>
                                </a>
                            }
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Spacer để tránh content bị che khi dùng fixed navbar -->
        <div class="navbar-spacer" aria-hidden="true"></div>
    </header>

    <div class="container-fluid px-0">
        <main role="main" class="main-content" id="main-content">
            @RenderBody()
        </main>
    </div>

    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-section">
                        <h3 class="footer-title">@JunTech.caidat.TieuDeWeb</h3>
                        <p class="footer-description">@JunTech.caidat.MoTaThem</p>
                        <address class="footer-address">
                            <div class="address-item">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                <span>@JunTech.caidat.DiaChi</span>
                            </div>
                            <div class="address-item">
                                <i class="fas fa-phone" aria-hidden="true"></i>
                                <a href="tel:@JunTech.caidat.SoDienThoai">@JunTech.caidat.SoDienThoai</a>
                            </div>
                            <div class="address-item">
                                <i class="fas fa-envelope" aria-hidden="true"></i>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                        </address>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h4 class="footer-subtitle">Khám Phá</h4>
                        <nav aria-label="Footer navigation - Khám phá">
                            <ul class="footer-links">
                                <li><a href="/phong-moi">Phòng mới</a></li>
                                <li><a href="/phong-noi-bat">Phòng nổi bật</a></li>
                                <li><a href="/gan-truong-hoc">Gần trường học</a></li>
                                <li><a href="/gan-benh-vien">Gần bệnh viện</a></li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h4 class="footer-subtitle">Hỗ Trợ</h4>
                        <nav aria-label="Footer navigation - Hỗ trợ">
                            <ul class="footer-links">
                                <li><a href="/ho-tro">Trung tâm hỗ trợ</a></li>
                                <li><a href="/faq">Câu hỏi thường gặp</a></li>
                                <li><a href="/huong-dan">Hướng dẫn đăng ký</a></li>
                                <li><a href="/chinh-sach">Chính sách</a></li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-section">
                        <h4 class="footer-subtitle">Đăng Ký Nhận Tin</h4>
                        <p class="newsletter-description">Nhận thông tin phòng trọ mới nhất và ưu đãi đặc biệt từ chúng tôi</p>
                        <form class="newsletter-form" aria-label="Đăng ký nhận tin">
                            <div class="input-group">
                                <input type="email" class="form-control" placeholder="Nhập email của bạn"
                                       aria-label="Email address" required>
                                <button class="btn btn-primary" type="submit" aria-label="Đăng ký nhận tin">
                                    <i class="fas fa-paper-plane" aria-hidden="true"></i>
                                </button>
                            </div>
                        </form>

                        <div class="social-links" role="group" aria-label="Liên kết mạng xã hội">
                            <a href="#" class="social-link" aria-label="Facebook" target="_blank" rel="noopener">
                                <i class="fab fa-facebook-f" aria-hidden="true"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="YouTube" target="_blank" rel="noopener">
                                <i class="fab fa-youtube" aria-hidden="true"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="Instagram" target="_blank" rel="noopener">
                                <i class="fab fa-instagram" aria-hidden="true"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="TikTok" target="_blank" rel="noopener">
                                <i class="fab fa-tiktok" aria-hidden="true"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="darkModeSwitch" aria-label="Chế độ tối">
                            <label class="form-check-label" for="darkModeSwitch">Chế độ tối</label>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="copyright">
                            &copy; 2025 Phòng Trọ Việt. Tất cả quyền được bảo lưu.
                            <a asp-area="" asp-controller="Home" asp-action="Privacy">Chính Sách Bảo Mật</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to top button -->
    <button id="backToTop" class="back-to-top" aria-label="Về đầu trang" title="Về đầu trang">
        <i class="fas fa-chevron-up" aria-hidden="true"></i>
    </button>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/7.0.0/signalr.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/dark-mode.js" asp-append-version="true"></script>

    <script>
        // Logout functionality
        document.body.addEventListener('click', function (e) {
            if (e.target && e.target.id === 'logoutBtn') {
                e.preventDefault();

                (async () => {
                    try {
                        const response = await fetch('/api/Auth/Logout', {
                            method: 'POST',
                            credentials: 'include',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            location.reload();
                        } else {
                            alert('Lỗi khi đăng xuất');
                        }
                    } catch {
                        alert('Lỗi kết nối API đăng xuất');
                    }
                })();
            }
        });

        // Back to top functionality
        window.addEventListener('scroll', function () {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function () {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
